import { supabase } from './supabase.js'

// 成就系统
class AchievementSystem {
  constructor() {
    this.achievements = new Map()
    this.initializeAchievements()
  }

  // 初始化成就定义
  initializeAchievements() {
    // 购买相关成就
    this.achievements.set('first_purchase', {
      id: 'first_purchase',
      name: '初次购买',
      description: '完成你在斯卡布罗集市的第一次购买',
      type: 'purchase',
      condition: { count: 1 },
      rewards: { xp: 50, coins: 100 },
      rarity: 'common'
    })

    this.achievements.set('frequent_buyer', {
      id: 'frequent_buyer',
      name: '常客',
      description: '完成10次购买',
      type: 'purchase',
      condition: { count: 10 },
      rewards: { xp: 200, coins: 300 },
      rarity: 'uncommon'
    })

    this.achievements.set('big_spender', {
      id: 'big_spender',
      name: '大买家',
      description: '单次购买金额超过1000元',
      type: 'purchase_amount',
      condition: { amount: 1000 },
      rewards: { xp: 300, coins: 500 },
      rarity: 'rare'
    })

    // 收集相关成就
    this.achievements.set('herb_collector', {
      id: 'herb_collector',
      name: '草本收集者',
      description: '收集三种不同的草本植物',
      type: 'collection',
      condition: { category: '草本香料', count: 3 },
      rewards: { xp: 100, coins: 200 },
      rarity: 'common'
    })

    this.achievements.set('herb_sage', {
      id: 'herb_sage',
      name: '草本贤者',
      description: '收集所有四种经典草本：芜荽、鼠尾草、迷迭香、百里香',
      type: 'collection_specific',
      condition: { 
        items: ['芜荽', '鼠尾草', '迷迭香', '百里香']
      },
      rewards: { xp: 500, coins: 1000 },
      rarity: 'epic'
    })

    // 社交相关成就
    this.achievements.set('diary_writer', {
      id: 'diary_writer',
      name: '日记作家',
      description: '发布5篇日记',
      type: 'diary_post',
      condition: { count: 5 },
      rewards: { xp: 150, coins: 150 },
      rarity: 'common'
    })

    this.achievements.set('social_butterfly', {
      id: 'social_butterfly',
      name: '社交达人',
      description: '获得100个日记点赞',
      type: 'diary_likes',
      condition: { count: 100 },
      rewards: { xp: 300, coins: 400 },
      rarity: 'uncommon'
    })

    this.achievements.set('morning_mist', {
      id: 'morning_mist',
      name: '晨雾行者',
      description: '创作了一篇晨雾行者诗篇',
      type: 'special_diary',
      condition: { type: 'morning_mist' },
      rewards: { xp: 1000, coins: 2000 },
      rarity: 'legendary'
    })

    // 探索相关成就
    this.achievements.set('explorer', {
      id: 'explorer',
      name: '探索者',
      description: '解锁3个新地点',
      type: 'exploration',
      condition: { count: 3 },
      rewards: { xp: 200, coins: 300 },
      rarity: 'uncommon'
    })

    this.achievements.set('world_traveler', {
      id: 'world_traveler',
      name: '世界旅行者',
      description: '解锁所有地点',
      type: 'exploration_complete',
      condition: { all_locations: true },
      rewards: { xp: 1000, coins: 2000 },
      rarity: 'legendary'
    })

    // 商业相关成就
    this.achievements.set('merchant_master', {
      id: 'merchant_master',
      name: '商户大师',
      description: '成功经营商户并达到100笔交易',
      type: 'merchant_sales',
      condition: { count: 100 },
      rewards: { xp: 800, coins: 1500 },
      rarity: 'epic'
    })

    this.achievements.set('product_creator', {
      id: 'product_creator',
      name: '商品创造者',
      description: '发布10个商品',
      type: 'product_creation',
      condition: { count: 10 },
      rewards: { xp: 300, coins: 500 },
      rarity: 'uncommon'
    })

    // 等级相关成就
    this.achievements.set('level_10', {
      id: 'level_10',
      name: '见习商人',
      description: '达到10级',
      type: 'level',
      condition: { level: 10 },
      rewards: { xp: 500, coins: 1000 },
      rarity: 'uncommon'
    })

    this.achievements.set('level_50', {
      id: 'level_50',
      name: '传奇商人',
      description: '达到50级',
      type: 'level',
      condition: { level: 50 },
      rewards: { xp: 2000, coins: 5000 },
      rarity: 'legendary'
    })

    // 特殊成就
    this.achievements.set('night_owl', {
      id: 'night_owl',
      name: '夜猫子',
      description: '在深夜时分完成10次交易',
      type: 'time_based',
      condition: { time: 'night', count: 10 },
      rewards: { xp: 200, coins: 300 },
      rarity: 'uncommon'
    })

    this.achievements.set('early_bird', {
      id: 'early_bird',
      name: '早起鸟',
      description: '在清晨时分完成5次交易',
      type: 'time_based',
      condition: { time: 'dawn', count: 5 },
      rewards: { xp: 150, coins: 250 },
      rarity: 'uncommon'
    })
  }

  // 检查用户成就
  async checkAchievements(userId, action, data) {
    try {
      const unlockedAchievements = []

      for (const [achievementId, achievement] of this.achievements) {
        // 检查用户是否已有此成就
        const { data: userBadges } = await supabase
          .from('users')
          .select('badges')
          .eq('id', userId)
          .single()

        const badges = userBadges?.badges || []
        const hasAchievement = badges.some(badge => badge.id === achievementId)

        if (hasAchievement) continue

        // 检查是否满足成就条件
        const meetsCondition = await this.checkCondition(userId, achievement, action, data)

        if (meetsCondition) {
          await this.unlockAchievement(userId, achievement)
          unlockedAchievements.push(achievement)
        }
      }

      return unlockedAchievements
    } catch (error) {
      console.error('检查成就失败:', error)
      return []
    }
  }

  // 检查成就条件
  async checkCondition(userId, achievement, action, data) {
    try {
      switch (achievement.type) {
        case 'purchase':
          return await this.checkPurchaseCondition(userId, achievement.condition)
        
        case 'purchase_amount':
          return data.amount >= achievement.condition.amount
        
        case 'collection':
          return await this.checkCollectionCondition(userId, achievement.condition)
        
        case 'collection_specific':
          return await this.checkSpecificCollectionCondition(userId, achievement.condition)
        
        case 'diary_post':
          return await this.checkDiaryPostCondition(userId, achievement.condition)
        
        case 'diary_likes':
          return await this.checkDiaryLikesCondition(userId, achievement.condition)
        
        case 'special_diary':
          return action === 'diary_post' && data.is_morning_mist
        
        case 'exploration':
          return await this.checkExplorationCondition(userId, achievement.condition)
        
        case 'exploration_complete':
          return await this.checkExplorationCompleteCondition(userId)
        
        case 'merchant_sales':
          return await this.checkMerchantSalesCondition(userId, achievement.condition)
        
        case 'product_creation':
          return await this.checkProductCreationCondition(userId, achievement.condition)
        
        case 'level':
          return data.level >= achievement.condition.level
        
        case 'time_based':
          return await this.checkTimeBasedCondition(userId, achievement.condition, data)
        
        default:
          return false
      }
    } catch (error) {
      console.error('检查成就条件失败:', error)
      return false
    }
  }

  // 检查购买条件
  async checkPurchaseCondition(userId, condition) {
    const { count } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('payment_status', 'completed')

    return count >= condition.count
  }

  // 检查收集条件
  async checkCollectionCondition(userId, condition) {
    const { data: orders } = await supabase
      .from('orders')
      .select(`
        products (
          category
        )
      `)
      .eq('user_id', userId)
      .eq('payment_status', 'completed')

    const categories = new Set()
    orders?.forEach(order => {
      if (order.products?.category === condition.category) {
        categories.add(order.products.category)
      }
    })

    return categories.size >= condition.count
  }

  // 检查特定收集条件
  async checkSpecificCollectionCondition(userId, condition) {
    const { data: orders } = await supabase
      .from('orders')
      .select(`
        products (
          name
        )
      `)
      .eq('user_id', userId)
      .eq('payment_status', 'completed')

    const collectedItems = new Set()
    orders?.forEach(order => {
      if (condition.items.includes(order.products?.name)) {
        collectedItems.add(order.products.name)
      }
    })

    return collectedItems.size >= condition.items.length
  }

  // 检查日记发布条件
  async checkDiaryPostCondition(userId, condition) {
    const { count } = await supabase
      .from('diary_entries')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    return count >= condition.count
  }

  // 检查日记点赞条件
  async checkDiaryLikesCondition(userId, condition) {
    const { data: entries } = await supabase
      .from('diary_entries')
      .select('likes')
      .eq('user_id', userId)

    const totalLikes = entries?.reduce((sum, entry) => sum + (entry.likes || 0), 0) || 0
    return totalLikes >= condition.count
  }

  // 检查探索条件
  async checkExplorationCondition(userId, condition) {
    const { count } = await supabase
      .from('user_explorations')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    return count >= condition.count
  }

  // 检查完整探索条件
  async checkExplorationCompleteCondition(userId) {
    const { count: userExplorations } = await supabase
      .from('user_explorations')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    // 假设总共有5个地点
    const totalLocations = 5
    return userExplorations >= totalLocations
  }

  // 检查商户销售条件
  async checkMerchantSalesCondition(userId, condition) {
    const { data: merchant } = await supabase
      .from('merchants')
      .select('total_sales')
      .eq('user_id', userId)
      .single()

    return merchant?.total_sales >= condition.count
  }

  // 检查商品创建条件
  async checkProductCreationCondition(userId, condition) {
    const { count } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('merchant_id', userId) // 这里需要通过商户ID查询

    return count >= condition.count
  }

  // 检查时间相关条件
  async checkTimeBasedCondition(userId, condition, data) {
    // 这里需要根据具体的时间条件来实现
    // 例如检查夜间交易次数等
    return false // 简化实现
  }

  // 解锁成就
  async unlockAchievement(userId, achievement) {
    try {
      const badge = {
        id: achievement.id,
        name: achievement.name,
        unlocked_at: new Date().toISOString()
      }

      // 添加徽章到用户
      await supabase
        .from('users')
        .update({
          badges: supabase.raw(`badges || '[${JSON.stringify(badge)}]'::jsonb`)
        })
        .eq('id', userId)

      // 给予奖励
      if (achievement.rewards.xp) {
        await supabase.rpc('add_user_xp', {
          user_id: userId,
          xp_amount: achievement.rewards.xp
        })
      }

      if (achievement.rewards.coins) {
        await supabase.rpc('add_vanilla_coins', {
          user_id: userId,
          coin_amount: achievement.rewards.coins
        })
      }

      // 发送通知
      await supabase
        .from('messages')
        .insert({
          sender_id: userId,
          recipient_id: userId,
          title: '成就解锁！',
          content: `恭喜解锁成就：${achievement.name}！获得 ${achievement.rewards.xp || 0} 经验值和 ${achievement.rewards.coins || 0} 香草币。`,
          type: 'system'
        })

      console.log(`🏆 用户 ${userId} 解锁成就: ${achievement.name}`)
    } catch (error) {
      console.error('解锁成就失败:', error)
    }
  }

  // 获取用户成就进度
  async getUserAchievementProgress(userId) {
    try {
      const { data: user } = await supabase
        .from('users')
        .select('badges')
        .eq('id', userId)
        .single()

      const userBadges = user?.badges || []
      const progress = []

      for (const [achievementId, achievement] of this.achievements) {
        const hasAchievement = userBadges.some(badge => badge.id === achievementId)
        const currentProgress = hasAchievement ? 100 : await this.calculateProgress(userId, achievement)

        progress.push({
          ...achievement,
          unlocked: hasAchievement,
          progress: currentProgress
        })
      }

      return progress
    } catch (error) {
      console.error('获取成就进度失败:', error)
      return []
    }
  }

  // 计算成就进度
  async calculateProgress(userId, achievement) {
    // 这里实现具体的进度计算逻辑
    // 返回0-100的进度百分比
    return 0
  }
}

// 创建全局成就系统实例
export const achievementSystem = new AchievementSystem()

export default AchievementSystem
