import express from 'express'
import { authMiddleware, optionalAuth } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { supabase } from '../services/supabase.js'

const router = express.Router()

// 获取公开日记列表
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const { type, limit = 20, offset = 0, user_id } = req.query
  
  let query = supabase
    .from('diary_entries')
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .eq('visibility', 'public')
    .order('created_at', { ascending: false })
  
  if (type) {
    query = query.eq('type', type)
  }
  
  if (user_id) {
    query = query.eq('user_id', user_id)
  }
  
  query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)
  
  const { data, error, count } = await query
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: count
    }
  })
}))

// 获取用户自己的日记列表
router.get('/my', authMiddleware, asyncHandler(async (req, res) => {
  const { type, visibility, limit = 20, offset = 0 } = req.query
  
  let query = supabase
    .from('diary_entries')
    .select('*')
    .eq('user_id', req.user.id)
    .order('created_at', { ascending: false })
  
  if (type) {
    query = query.eq('type', type)
  }
  
  if (visibility) {
    query = query.eq('visibility', visibility)
  }
  
  query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)
  
  const { data, error, count } = await query
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: count
    }
  })
}))

// 获取日记详情
router.get('/:id', optionalAuth, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const { data: entry, error } = await supabase
    .from('diary_entries')
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .eq('id', id)
    .single()
  
  if (error) {
    return res.status(404).json({
      success: false,
      error: '日记不存在'
    })
  }
  
  // 检查访问权限
  if (entry.visibility === 'private' && (!req.user || entry.user_id !== req.user.id)) {
    return res.status(403).json({
      success: false,
      error: '无权访问此日记'
    })
  }
  
  // 获取评论
  const { data: comments } = await supabase
    .from('diary_comments')
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .eq('diary_entry_id', id)
    .order('created_at', { ascending: true })
  
  res.json({
    success: true,
    data: {
      ...entry,
      comments: comments || []
    }
  })
}))

// 创建日记
router.post('/', authMiddleware, asyncHandler(async (req, res) => {
  const { title, content, type = 'vanilla_diary', visibility = 'public', tags } = req.body
  
  if (!title || !content) {
    return res.status(400).json({
      success: false,
      error: '标题和内容不能为空'
    })
  }
  
  // 检查是否触发晨雾行者诗篇
  let is_morning_mist = false
  if (type === 'wanderer_poem' && Math.random() < 0.05) { // 5% 概率
    is_morning_mist = true
  }
  
  const { data: entry, error } = await supabase
    .from('diary_entries')
    .insert({
      user_id: req.user.id,
      title,
      content,
      type,
      visibility,
      is_morning_mist,
      tags
    })
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .single()
  
  if (error) throw error
  
  // 给用户增加经验值
  let xpReward = 10
  if (is_morning_mist) {
    xpReward = 100 // 晨雾行者诗篇给更多经验值
    
    // 解锁晨雾行者徽章
    await supabase
      .from('users')
      .update({
        badges: supabase.raw(`
          CASE 
            WHEN badges @> '[{"id": "morning_mist"}]'::jsonb 
            THEN badges 
            ELSE badges || '[{"id": "morning_mist", "name": "晨雾行者", "unlocked_at": "' + NOW() + '"}]'::jsonb
          END
        `)
      })
      .eq('id', req.user.id)
    
    // 给予香草币奖励
    await supabase.rpc('add_vanilla_coins', {
      user_id: req.user.id,
      coin_amount: 200
    })
  }
  
  await supabase.rpc('add_user_xp', {
    user_id: req.user.id,
    xp_amount: xpReward
  })
  
  res.status(201).json({
    success: true,
    data: entry,
    message: is_morning_mist 
      ? '恭喜！你创作了一篇晨雾行者诗篇，获得特殊奖励！' 
      : `日记发布成功！获得 ${xpReward} 经验值。`
  })
}))

// 更新日记
router.put('/:id', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  const { title, content, visibility, tags } = req.body
  
  const { data: entry, error } = await supabase
    .from('diary_entries')
    .update({
      title,
      content,
      visibility,
      tags,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .eq('user_id', req.user.id)
    .select()
    .single()
  
  if (error) {
    return res.status(404).json({
      success: false,
      error: '日记不存在或无权修改'
    })
  }
  
  res.json({
    success: true,
    data: entry,
    message: '日记更新成功'
  })
}))

// 删除日记
router.delete('/:id', authMiddleware, asyncHandler(async (req, res) => {
  const { error } = await supabase
    .from('diary_entries')
    .delete()
    .eq('id', req.params.id)
    .eq('user_id', req.user.id)
  
  if (error) {
    return res.status(404).json({
      success: false,
      error: '日记不存在或无权删除'
    })
  }
  
  res.json({
    success: true,
    message: '日记删除成功'
  })
}))

// 点赞日记
router.post('/:id/like', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const result = await supabase.rpc('like_diary_entry', {
    entry_id: id,
    user_id: req.user.id
  })
  
  if (result.error) throw result.error
  
  const likeResult = result.data[0]
  
  res.json({
    success: likeResult.success,
    data: {
      new_like_count: likeResult.new_like_count
    },
    message: likeResult.success ? '操作成功' : '操作失败'
  })
}))

// 添加评论
router.post('/:id/comments', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  const { content } = req.body
  
  if (!content || content.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: '评论内容不能为空'
    })
  }
  
  // 检查日记是否存在且可访问
  const { data: entry, error: entryError } = await supabase
    .from('diary_entries')
    .select('user_id, visibility')
    .eq('id', id)
    .single()
  
  if (entryError || !entry) {
    return res.status(404).json({
      success: false,
      error: '日记不存在'
    })
  }
  
  if (entry.visibility === 'private' && entry.user_id !== req.user.id) {
    return res.status(403).json({
      success: false,
      error: '无权评论此日记'
    })
  }
  
  // 添加评论
  const { data: comment, error } = await supabase
    .from('diary_comments')
    .insert({
      diary_entry_id: id,
      user_id: req.user.id,
      content: content.trim()
    })
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .single()
  
  if (error) throw error
  
  // 更新日记评论数
  await supabase
    .from('diary_entries')
    .update({ 
      comments: supabase.raw('comments + 1')
    })
    .eq('id', id)
  
  // 给评论者增加经验值
  await supabase.rpc('add_user_xp', {
    user_id: req.user.id,
    xp_amount: 5
  })
  
  // 如果不是自己的日记，给日记作者发送通知
  if (entry.user_id !== req.user.id) {
    await supabase
      .from('messages')
      .insert({
        sender_id: req.user.id,
        recipient_id: entry.user_id,
        title: '新评论',
        content: `有人评论了你的日记`,
        type: 'system'
      })
  }
  
  res.status(201).json({
    success: true,
    data: comment,
    message: '评论添加成功！获得 5 经验值。'
  })
}))

// 获取热门日记
router.get('/trending/list', optionalAuth, asyncHandler(async (req, res) => {
  const { limit = 10, timeframe = '7d' } = req.query
  
  // 计算时间范围
  const timeframeDays = timeframe === '1d' ? 1 : timeframe === '7d' ? 7 : 30
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - timeframeDays)
  
  const { data, error } = await supabase
    .from('diary_entries')
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .eq('visibility', 'public')
    .gte('created_at', startDate.toISOString())
    .order('likes', { ascending: false })
    .limit(parseInt(limit))
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    timeframe,
    message: `过去${timeframeDays}天的热门日记`
  })
}))

// 搜索日记
router.get('/search/entries', optionalAuth, asyncHandler(async (req, res) => {
  const { q: searchTerm, type, limit = 20, offset = 0 } = req.query
  
  if (!searchTerm) {
    return res.status(400).json({
      success: false,
      error: '搜索关键词不能为空'
    })
  }
  
  let query = supabase
    .from('diary_entries')
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .eq('visibility', 'public')
    .or(`title.ilike.%${searchTerm}%,content.ilike.%${searchTerm}%`)
    .order('created_at', { ascending: false })
  
  if (type) {
    query = query.eq('type', type)
  }
  
  query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)
  
  const { data, error, count } = await query
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    searchTerm,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: count
    }
  })
}))

export default router
