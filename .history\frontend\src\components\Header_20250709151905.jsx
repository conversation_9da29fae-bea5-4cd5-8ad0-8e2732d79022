import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  Home, 
  ShoppingBag, 
  BookOpen, 
  Map, 
  User, 
  Menu, 
  X,
  Coins,
  Star
} from 'lucide-react'
import { supabase } from '../services/supabase'

const Header = ({ user }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const location = useLocation()

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    setIsMenuOpen(false)
  }

  const handleSignIn = async () => {
    await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: window.location.origin
      }
    })
  }

  const navItems = [
    { path: '/', label: '首页', icon: Home },
    { path: '/market', label: '集市', icon: ShoppingBag },
    { path: '/diary', label: '日记', icon: BookOpen },
    { path: '/map', label: '地图', icon: Map },
  ]

  const isActivePath = (path) => {
    return location.pathname === path
  }

  return (
    <header className="bg-herb-green shadow-lg sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-herb-gold rounded-full flex items-center justify-center">
              <span className="text-parchment font-medieval font-bold text-lg">S</span>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-parchment font-medieval text-xl font-semibold">
                斯卡布罗集市
              </h1>
              <p className="text-parchment/80 text-xs">
                Scarborough Fair
              </p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map(({ path, label, icon: Icon }) => (
              <Link
                key={path}
                to={path}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors duration-200 ${
                  isActivePath(path)
                    ? 'bg-herb-gold text-herb-green'
                    : 'text-parchment hover:bg-rosemary-purple'
                }`}
              >
                <Icon size={18} />
                <span className="font-medium">{label}</span>
              </Link>
            ))}
          </nav>

          {/* User Section */}
          <div className="flex items-center space-x-4">
            {user ? (
              <>
                {/* User Stats (Desktop) */}
                <div className="hidden lg:flex items-center space-x-4 text-parchment">
                  <div className="flex items-center space-x-1">
                    <Star size={16} className="text-herb-gold" />
                    <span className="text-sm">Lv.{user.level || 1}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Coins size={16} className="text-herb-gold" />
                    <span className="text-sm">{user.vanilla_coins || 0}</span>
                  </div>
                </div>

                {/* User Menu */}
                <div className="relative">
                  <button
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                    className="flex items-center space-x-2 bg-rosemary-purple hover:bg-rosemary-purple/80 text-parchment px-3 py-2 rounded-lg transition-colors duration-200"
                  >
                    <User size={18} />
                    <span className="hidden sm:block font-medium">
                      {user.username || '流浪者'}
                    </span>
                  </button>

                  {/* Dropdown Menu */}
                  {isMenuOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-parchment border-2 border-sage-gray rounded-lg shadow-xl z-50">
                      <div className="py-2">
                        <Link
                          to="/profile"
                          className="block px-4 py-2 text-herb-green hover:bg-sage-gray/20 transition-colors duration-200"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          个人档案
                        </Link>
                        <Link
                          to="/merchant"
                          className="block px-4 py-2 text-herb-green hover:bg-sage-gray/20 transition-colors duration-200"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          我的店铺
                        </Link>
                        <Link
                          to="/orders"
                          className="block px-4 py-2 text-herb-green hover:bg-sage-gray/20 transition-colors duration-200"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          订单记录
                        </Link>
                        <hr className="my-2 border-sage-gray/30" />
                        <button
                          onClick={handleSignOut}
                          className="block w-full text-left px-4 py-2 text-rosemary-purple hover:bg-sage-gray/20 transition-colors duration-200"
                        >
                          退出登录
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <button
                onClick={handleSignIn}
                className="bg-herb-gold hover:bg-herb-gold/80 text-herb-green px-4 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                登录
              </button>
            )}

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden text-parchment p-2"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-herb-gold/30 py-4">
            <nav className="space-y-2">
              {navItems.map(({ path, label, icon: Icon }) => (
                <Link
                  key={path}
                  to={path}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200 ${
                    isActivePath(path)
                      ? 'bg-herb-gold text-herb-green'
                      : 'text-parchment hover:bg-rosemary-purple'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Icon size={20} />
                  <span className="font-medium">{label}</span>
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
