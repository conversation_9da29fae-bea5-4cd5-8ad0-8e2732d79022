# 环境变量示例文件
# 复制此文件为 .env 并填入实际值

# Supabase 配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# Stripe 支付配置
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# JWT 密钥
JWT_SECRET=your-super-secret-jwt-key

# 应用配置
NODE_ENV=development
PORT=5000

# 前端配置
VITE_API_BASE_URL=http://localhost:5000/api
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# 数据库配置（如果使用本地数据库）
DATABASE_URL=postgresql://username:password@localhost:5432/scarborough_fair

# Redis 配置
REDIS_URL=redis://localhost:6379

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# 监控配置
SENTRY_DSN=your-sentry-dsn
ANALYTICS_ID=your-analytics-id

# 第三方服务配置
WEATHER_API_KEY=your-weather-api-key
MAP_API_KEY=your-map-api-key

# 开发配置
DEBUG=true
HOT_RELOAD=true
