import React, { useState, useEffect } from 'react'
import { 
  BookOpen, 
  Play, 
  CheckCircle, 
  Clock, 
  Award, 
  Users,
  TrendingUp,
  Lightbulb,
  Target,
  Star
} from 'lucide-react'

const MerchantTrainingPage = ({ user }) => {
  const [currentModule, setCurrentModule] = useState(null)
  const [completedModules, setCompletedModules] = useState([])
  const [userProgress, setUserProgress] = useState(0)

  // 培训模块数据
  const trainingModules = [
    {
      id: 'basics',
      title: '集市基础知识',
      description: '了解斯卡布罗集市的历史、文化和基本运营规则',
      duration: '30分钟',
      difficulty: 'beginner',
      icon: '📚',
      lessons: [
        {
          id: 'history',
          title: '斯卡布罗集市的历史',
          content: `
            斯卡布罗集市源于古老的英国民谣，承载着数百年的商贸传统。
            在这里，每一种草本都有其独特的故事：
            
            🌿 **芜荽 (Parsley)** - 象征着新的开始和希望
            🌿 **鼠尾草 (Sage)** - 代表智慧和经验
            🌿 **迷迭香 (Rosemary)** - 寓意记忆和忠诚
            🌿 **百里香 (Thyme)** - 象征勇气和力量
            
            作为商户，您将成为这个传统的传承者。
          `,
          quiz: [
            {
              question: '在斯卡布罗集市中，迷迭香象征什么？',
              options: ['希望', '智慧', '记忆和忠诚', '勇气'],
              correct: 2
            }
          ]
        },
        {
          id: 'culture',
          title: '集市文化与价值观',
          content: `
            斯卡布罗集市不仅是交易场所，更是一个充满诗意的社区：
            
            ✨ **诚信为本** - 每一次交易都建立在信任基础上
            🎭 **故事驱动** - 每个商品都有其独特的故事
            🌟 **品质至上** - 追求卓越的商品和服务
            🤝 **社区互助** - 商户之间相互支持，共同成长
            
            在这里，您不只是在销售商品，而是在分享文化和传承传统。
          `
        }
      ]
    },
    {
      id: 'setup',
      title: '商户开店指南',
      description: '从零开始，学习如何在集市中建立您的店铺',
      duration: '45分钟',
      difficulty: 'beginner',
      icon: '🏪',
      lessons: [
        {
          id: 'registration',
          title: '商户注册流程',
          content: `
            成为斯卡布罗集市的商户需要以下步骤：
            
            1. **完善个人档案** - 确保您的用户等级达到5级
            2. **提交商户申请** - 填写详细的商户信息
            3. **选择经营类别** - 选择您要经营的商品类型
            4. **设置店铺信息** - 包括店名、描述、营业时间等
            5. **上传必要文件** - 身份证明、经营许可等
            6. **等待审核** - 通常需要1-3个工作日
            
            💡 **小贴士**: 详细的店铺描述和专业的头像能提高审核通过率
          `
        },
        {
          id: 'storefront',
          title: '店铺装修与设计',
          content: `
            一个吸引人的店铺是成功的第一步：
            
            🎨 **视觉设计**
            - 选择符合集市风格的配色方案
            - 使用高质量的商品图片
            - 保持页面布局整洁有序
            
            📝 **内容策略**
            - 编写引人入胜的店铺故事
            - 详细描述商品特色和用途
            - 定期更新店铺动态
            
            🏷️ **分类管理**
            - 合理设置商品分类
            - 使用准确的标签系统
            - 优化搜索关键词
          `
        }
      ]
    },
    {
      id: 'products',
      title: '商品管理精要',
      description: '学习如何有效管理您的商品库存和定价策略',
      duration: '60分钟',
      difficulty: 'intermediate',
      icon: '📦',
      lessons: [
        {
          id: 'pricing',
          title: '定价策略',
          content: `
            合理的定价是商业成功的关键：
            
            💰 **成本分析**
            - 计算商品成本（采购、运输、包装）
            - 考虑平台手续费（5%）
            - 预留合理的利润空间
            
            📊 **市场调研**
            - 研究同类商品的价格区间
            - 分析竞争对手的定价策略
            - 关注市场需求变化
            
            🎯 **定价技巧**
            - 使用心理定价（如99元而非100元）
            - 设置阶梯价格（批量优惠）
            - 定期调整价格以适应市场
          `
        },
        {
          id: 'inventory',
          title: '库存管理',
          content: `
            有效的库存管理能避免缺货和积压：
            
            📈 **需求预测**
            - 分析历史销售数据
            - 关注季节性变化
            - 考虑特殊事件影响
            
            🔄 **补货策略**
            - 设置安全库存水位
            - 建立供应商关系
            - 制定紧急补货计划
            
            📊 **数据监控**
            - 跟踪库存周转率
            - 监控滞销商品
            - 优化库存结构
          `
        }
      ]
    },
    {
      id: 'marketing',
      title: '营销与推广',
      description: '掌握在集市中推广商品和吸引客户的有效方法',
      duration: '50分钟',
      difficulty: 'intermediate',
      icon: '📢',
      lessons: [
        {
          id: 'storytelling',
          title: '商品故事营销',
          content: `
            在斯卡布罗集市，每个商品都应该有自己的故事：
            
            📖 **故事元素**
            - 商品的来源和制作过程
            - 背后的文化意义
            - 使用场景和体验
            
            ✍️ **写作技巧**
            - 使用生动的描述语言
            - 融入情感元素
            - 保持真实可信
            
            🎭 **角色扮演**
            - 以NPC的身份介绍商品
            - 创造沉浸式的购物体验
            - 与集市主题保持一致
          `
        },
        {
          id: 'events',
          title: '参与集市活动',
          content: `
            积极参与集市活动能大大提升知名度：
            
            🎪 **定期活动**
            - 草本节庆：展示相关商品
            - 月圆狩猎节：推广狩猎装备
            - 晴日集市：参与折扣活动
            
            🏆 **竞赛活动**
            - 最佳商户评选
            - 创意商品大赛
            - 客户满意度评比
            
            🤝 **合作机会**
            - 与其他商户联合促销
            - 参与主题活动策划
            - 分享经营经验
          `
        }
      ]
    },
    {
      id: 'advanced',
      title: '高级经营策略',
      description: '学习数据分析、客户关系管理等高级商业技能',
      duration: '75分钟',
      difficulty: 'advanced',
      icon: '🎯',
      lessons: [
        {
          id: 'analytics',
          title: '数据分析与洞察',
          content: `
            数据驱动的决策能让您的生意更上一层楼：
            
            📊 **关键指标**
            - 销售额和利润率
            - 客户获取成本
            - 复购率和客户生命周期价值
            
            🔍 **分析工具**
            - 使用商户后台的数据面板
            - 跟踪商品浏览和转化率
            - 分析客户行为模式
            
            💡 **优化策略**
            - A/B测试不同的商品描述
            - 优化商品图片和价格
            - 改进客户服务流程
          `
        },
        {
          id: 'scaling',
          title: '业务扩展策略',
          content: `
            当您的生意稳定后，考虑以下扩展策略：
            
            🌱 **产品线扩展**
            - 增加相关商品类别
            - 开发独家定制商品
            - 探索新的市场需求
            
            🤝 **合作伙伴关系**
            - 与供应商建立长期合作
            - 寻找互补的商户合作
            - 考虑代理其他品牌
            
            🌍 **市场拓展**
            - 研究其他地区的需求
            - 考虑线下销售渠道
            - 探索B2B业务机会
          `
        }
      ]
    }
  ]

  useEffect(() => {
    if (user) {
      loadUserProgress()
    }
  }, [user])

  const loadUserProgress = async () => {
    // 模拟加载用户培训进度
    const completed = ['basics', 'setup']
    setCompletedModules(completed)
    setUserProgress((completed.length / trainingModules.length) * 100)
  }

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-700'
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-700'
      case 'advanced':
        return 'bg-red-100 text-red-700'
      default:
        return 'bg-gray-100 text-gray-700'
    }
  }

  const getDifficultyLabel = (difficulty) => {
    switch (difficulty) {
      case 'beginner':
        return '初级'
      case 'intermediate':
        return '中级'
      case 'advanced':
        return '高级'
      default:
        return '未知'
    }
  }

  const startModule = (module) => {
    setCurrentModule(module)
  }

  const completeModule = async (moduleId) => {
    if (!completedModules.includes(moduleId)) {
      const newCompleted = [...completedModules, moduleId]
      setCompletedModules(newCompleted)
      setUserProgress((newCompleted.length / trainingModules.length) * 100)
      
      // 给予奖励
      alert(`恭喜完成培训模块！获得 100 经验值和 50 香草币！`)
    }
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🎓</div>
        <h2 className="text-2xl font-medieval text-herb-green mb-4">商户培训中心</h2>
        <p className="text-sage-gray mb-6">登录后开始您的商户培训之旅</p>
        <button className="btn-medieval">立即登录</button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-medieval text-herb-green mb-2">商户培训中心</h1>
        <p className="text-sage-gray">从新手到专家，全面提升您的经营技能</p>
      </div>

      {/* Progress Overview */}
      <div className="card-medieval">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-medieval text-herb-green">学习进度</h3>
          <span className="text-sm text-sage-gray">
            {completedModules.length} / {trainingModules.length} 模块完成
          </span>
        </div>
        
        <div className="progress-bar mb-4">
          <div 
            className="progress-fill" 
            style={{ width: `${userProgress}%` }}
          ></div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-herb-green">{Math.round(userProgress)}%</div>
            <div className="text-sm text-sage-gray">完成度</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-rosemary-purple">{completedModules.length}</div>
            <div className="text-sm text-sage-gray">已完成</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-herb-gold">
              {completedModules.length * 100}
            </div>
            <div className="text-sm text-sage-gray">获得经验</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-sage-gray">
              {userProgress >= 100 ? '🏆' : '📚'}
            </div>
            <div className="text-sm text-sage-gray">
              {userProgress >= 100 ? '专家' : '学习中'}
            </div>
          </div>
        </div>
      </div>

      {/* Training Modules */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {trainingModules.map((module) => {
          const isCompleted = completedModules.includes(module.id)
          const isLocked = module.id !== 'basics' && !completedModules.includes(trainingModules[trainingModules.findIndex(m => m.id === module.id) - 1]?.id)
          
          return (
            <div
              key={module.id}
              className={`card-medieval ${isCompleted ? 'border-green-300 bg-green-50' : isLocked ? 'opacity-60' : ''}`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="text-3xl">{module.icon}</div>
                <div className="flex items-center space-x-2">
                  <span className={`text-xs px-2 py-1 rounded ${getDifficultyColor(module.difficulty)}`}>
                    {getDifficultyLabel(module.difficulty)}
                  </span>
                  {isCompleted && <CheckCircle size={20} className="text-green-500" />}
                  {isLocked && <Clock size={20} className="text-sage-gray" />}
                </div>
              </div>

              <h3 className="text-lg font-medieval text-herb-green mb-2">
                {module.title}
              </h3>
              <p className="text-sm text-sage-gray mb-4 leading-relaxed">
                {module.description}
              </p>

              <div className="flex items-center justify-between text-sm text-sage-gray mb-4">
                <div className="flex items-center space-x-1">
                  <Clock size={14} />
                  <span>{module.duration}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <BookOpen size={14} />
                  <span>{module.lessons.length} 课程</span>
                </div>
              </div>

              <button
                onClick={() => startModule(module)}
                disabled={isLocked}
                className={`w-full py-2 px-4 rounded-lg transition-colors duration-200 ${
                  isLocked
                    ? 'bg-sage-gray/20 text-sage-gray cursor-not-allowed'
                    : isCompleted
                      ? 'bg-green-100 text-green-700 hover:bg-green-200'
                      : 'btn-medieval'
                }`}
              >
                {isLocked ? '🔒 未解锁' : isCompleted ? '复习课程' : '开始学习'}
              </button>
            </div>
          )
        })}
      </div>

      {/* Module Detail Modal */}
      {currentModule && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-parchment rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">{currentModule.icon}</div>
                  <div>
                    <h2 className="text-2xl font-medieval text-herb-green">
                      {currentModule.title}
                    </h2>
                    <p className="text-sage-gray">{currentModule.description}</p>
                  </div>
                </div>
                <button
                  onClick={() => setCurrentModule(null)}
                  className="text-sage-gray hover:text-herb-green"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-6">
                {currentModule.lessons.map((lesson, index) => (
                  <div key={lesson.id} className="border border-sage-gray/30 rounded-lg p-4">
                    <h3 className="text-lg font-medieval text-herb-green mb-3">
                      {index + 1}. {lesson.title}
                    </h3>
                    <div className="prose prose-sm max-w-none text-sage-gray whitespace-pre-line">
                      {lesson.content}
                    </div>
                    
                    {lesson.quiz && (
                      <div className="mt-4 p-4 bg-herb-green/10 rounded-lg">
                        <h4 className="font-medium text-herb-green mb-2">课程测验</h4>
                        <p className="text-sm text-sage-gray mb-3">{lesson.quiz[0].question}</p>
                        <div className="space-y-2">
                          {lesson.quiz[0].options.map((option, optionIndex) => (
                            <label key={optionIndex} className="flex items-center space-x-2">
                              <input type="radio" name={`quiz-${lesson.id}`} />
                              <span className="text-sm">{option}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex justify-between mt-6">
                <button
                  onClick={() => setCurrentModule(null)}
                  className="px-6 py-2 border border-sage-gray rounded-lg hover:bg-sage-gray/20"
                >
                  稍后学习
                </button>
                <button
                  onClick={() => {
                    completeModule(currentModule.id)
                    setCurrentModule(null)
                  }}
                  className="btn-medieval"
                >
                  完成学习
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MerchantTrainingPage
