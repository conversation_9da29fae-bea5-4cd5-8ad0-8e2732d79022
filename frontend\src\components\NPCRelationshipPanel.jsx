import React, { useState, useEffect } from 'react'
import { 
  Heart, 
  MessageCircle, 
  Gift, 
  Star, 
  Lock,
  Crown,
  Music,
  Leaf,
  Target
} from 'lucide-react'

const NPCRelationshipPanel = ({ user }) => {
  const [friendships, setFriendships] = useState([])
  const [selectedNPC, setSelectedNPC] = useState(null)
  const [showDialog, setShowDialog] = useState(false)

  // NPC 数据
  const npcData = {
    lyrick: {
      id: 'lyrick',
      name: '莱瑞克',
      title: '游吟诗人',
      description: '一位优雅的游吟诗人，总是带着他的鲁特琴在集市中演奏',
      icon: <Music className="text-herb-gold" size={24} />,
      personality: '温和、博学、神秘',
      likes: ['音乐', '诗歌', '古老的故事', '香草茶'],
      dislikes: ['噪音', '粗鲁的行为', '急躁'],
      location: '集市中央广场',
      backstory: `莱瑞克是一位来自远方的游吟诗人，他的歌声能够治愈人心。
        据说他知道许多古老的秘密和传说，但只有真正的朋友才能听到这些故事。`,
      friendship_rewards: {
        25: { type: 'song', name: '古老的摇篮曲', effect: '每日获得额外经验值' },
        50: { type: 'story', name: '集市的起源', effect: '解锁隐藏剧情线' },
        75: { type: 'skill', name: '诗人的灵感', effect: '日记获得双倍点赞' },
        100: { type: 'secret', name: '莱瑞克的真实身份', effect: '解锁传奇剧情' }
      }
    },
    flora: {
      id: 'flora',
      name: '芙萝拉',
      title: '草本商人',
      description: '温和的草本专家，经营着集市中最好的草本花园',
      icon: <Leaf className="text-herb-green" size={24} />,
      personality: '温柔、耐心、智慧',
      likes: ['植物', '自然', '帮助他人', '宁静的早晨'],
      dislikes: ['破坏环境', '暴力', '不尊重自然'],
      location: '草本花园',
      backstory: `芙萝拉从小就与植物有着特殊的联系，她能够感受到每一株草本的情绪。
        她的花园不仅是商店，更是一个治愈心灵的圣地。`,
      friendship_rewards: {
        25: { type: 'blessing', name: '花园祝福', effect: '草本商品价格优惠10%' },
        50: { type: 'knowledge', name: '草本秘籍', effect: '解锁稀有草本配方' },
        75: { type: 'garden_access', name: '私人花园', effect: '进入芙萝拉的秘密花园' },
        100: { type: 'nature_bond', name: '自然之契', effect: '与植物沟通的能力' }
      }
    },
    kane: {
      id: 'kane',
      name: '凯恩',
      title: '伶仃猎手',
      description: '沉默寡言的猎手，独自居住在集市边缘的小屋中',
      icon: <Target className="text-sage-gray" size={24} />,
      personality: '沉默、专注、忠诚',
      likes: ['狩猎', '安静', '诚实的人', '满月之夜'],
      dislikes: ['喧闹', '虚伪', '浪费食物'],
      location: '猎人小屋',
      backstory: `凯恩曾经是王室的护卫，但因为某种原因选择了隐居生活。
        他的箭术无人能及，但很少有人知道他内心的故事。`,
      friendship_rewards: {
        25: { type: 'training', name: '基础箭术', effect: '狩猎任务成功率提升' },
        50: { type: 'equipment', name: '猎人装备', effect: '获得特殊狩猎工具' },
        75: { type: 'hunt_partner', name: '狩猎伙伴', effect: '与凯恩一起狩猎' },
        100: { type: 'past_revealed', name: '过往真相', effect: '了解凯恩的真实身份' }
      }
    }
  }

  useEffect(() => {
    if (user) {
      loadFriendships()
    }
  }, [user])

  const loadFriendships = async () => {
    try {
      const response = await fetch('/api/story/npc/friendships', {
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })
      
      const result = await response.json()
      if (result.success) {
        setFriendships(result.data)
      }
    } catch (error) {
      console.error('加载友谊度失败:', error)
    }
  }

  const interactWithNPC = async (npcId, interactionType) => {
    try {
      const response = await fetch(`/api/story/npc/${npcId}/interact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.access_token}`
        },
        body: JSON.stringify({
          interaction_type: interactionType
        })
      })
      
      const result = await response.json()
      if (result.success) {
        // 更新友谊度
        loadFriendships()
        
        // 显示互动结果
        alert(result.message)
      }
    } catch (error) {
      console.error('NPC互动失败:', error)
    }
  }

  const getFriendshipLevel = (npcId) => {
    const friendship = friendships.find(f => f.npc_id === npcId)
    return friendship?.friendship_level || 0
  }

  const getFriendshipTitle = (level) => {
    if (level >= 100) return '挚友'
    if (level >= 75) return '好友'
    if (level >= 50) return '朋友'
    if (level >= 25) return '熟人'
    return '陌生人'
  }

  const getAvailableRewards = (npcId, level) => {
    const npc = npcData[npcId]
    if (!npc) return []
    
    return Object.entries(npc.friendship_rewards)
      .filter(([threshold]) => level >= parseInt(threshold))
      .map(([threshold, reward]) => ({ threshold: parseInt(threshold), ...reward }))
  }

  const getNextReward = (npcId, level) => {
    const npc = npcData[npcId]
    if (!npc) return null
    
    const nextThreshold = Object.keys(npc.friendship_rewards)
      .map(t => parseInt(t))
      .find(threshold => level < threshold)
    
    return nextThreshold ? {
      threshold: nextThreshold,
      ...npc.friendship_rewards[nextThreshold]
    } : null
  }

  if (!user) return null

  return (
    <div className="space-y-6">
      {/* NPC 关系概览 */}
      <div className="grid md:grid-cols-3 gap-6">
        {Object.values(npcData).map((npc) => {
          const friendship = friendships.find(f => f.npc_id === npc.id)
          const level = friendship?.friendship_level || 0
          const progress = friendship?.progress || 0
          const title = getFriendshipTitle(level)
          
          return (
            <div
              key={npc.id}
              className="card-medieval cursor-pointer hover:shadow-lg transition-shadow duration-200"
              onClick={() => setSelectedNPC(npc)}
            >
              {/* NPC 头像和基本信息 */}
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-herb-green/20 to-rosemary-purple/20 rounded-full flex items-center justify-center">
                  {npc.icon}
                </div>
                <div>
                  <h3 className="text-lg font-medieval text-herb-green">{npc.name}</h3>
                  <p className="text-sm text-sage-gray">{npc.title}</p>
                  <p className="text-xs text-rosemary-purple">{title}</p>
                </div>
              </div>

              {/* 友谊度进度条 */}
              <div className="mb-4">
                <div className="flex justify-between text-sm text-sage-gray mb-1">
                  <span>友谊度</span>
                  <span>{level}/100</span>
                </div>
                <div className="progress-bar">
                  <div 
                    className="progress-fill bg-gradient-to-r from-herb-green to-herb-gold"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
              </div>

              {/* 快速互动按钮 */}
              <div className="flex space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    interactWithNPC(npc.id, 'talk')
                  }}
                  className="flex-1 flex items-center justify-center space-x-1 py-2 px-3 bg-herb-green/20 text-herb-green rounded-lg hover:bg-herb-green/30 transition-colors duration-200"
                >
                  <MessageCircle size={14} />
                  <span className="text-xs">对话</span>
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    interactWithNPC(npc.id, 'gift')
                  }}
                  className="flex-1 flex items-center justify-center space-x-1 py-2 px-3 bg-herb-gold/20 text-herb-gold rounded-lg hover:bg-herb-gold/30 transition-colors duration-200"
                >
                  <Gift size={14} />
                  <span className="text-xs">送礼</span>
                </button>
              </div>

              {/* 下一个奖励预览 */}
              {(() => {
                const nextReward = getNextReward(npc.id, level)
                return nextReward && (
                  <div className="mt-3 p-2 bg-rosemary-purple/10 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Lock size={12} className="text-rosemary-purple" />
                      <span className="text-xs text-rosemary-purple">
                        {nextReward.threshold}级解锁: {nextReward.name}
                      </span>
                    </div>
                  </div>
                )
              })()}
            </div>
          )
        })}
      </div>

      {/* NPC 详情对话框 */}
      {selectedNPC && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-parchment rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <div className="w-20 h-20 bg-gradient-to-br from-herb-green/20 to-rosemary-purple/20 rounded-full flex items-center justify-center">
                    {selectedNPC.icon}
                  </div>
                  <div>
                    <h2 className="text-2xl font-medieval text-herb-green">{selectedNPC.name}</h2>
                    <p className="text-sage-gray">{selectedNPC.title}</p>
                    <p className="text-sm text-rosemary-purple">
                      {getFriendshipTitle(getFriendshipLevel(selectedNPC.id))}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedNPC(null)}
                  className="text-sage-gray hover:text-herb-green"
                >
                  ✕
                </button>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                {/* 基本信息 */}
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medieval text-herb-green mb-2">基本信息</h3>
                    <p className="text-sage-gray text-sm leading-relaxed">
                      {selectedNPC.description}
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium text-herb-green mb-2">性格特点</h4>
                    <p className="text-sage-gray text-sm">{selectedNPC.personality}</p>
                  </div>

                  <div>
                    <h4 className="font-medium text-herb-green mb-2">喜好</h4>
                    <div className="flex flex-wrap gap-1">
                      {selectedNPC.likes.map((like, index) => (
                        <span key={index} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                          {like}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-herb-green mb-2">厌恶</h4>
                    <div className="flex flex-wrap gap-1">
                      {selectedNPC.dislikes.map((dislike, index) => (
                        <span key={index} className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                          {dislike}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-herb-green mb-2">常驻地点</h4>
                    <p className="text-sage-gray text-sm">{selectedNPC.location}</p>
                  </div>
                </div>

                {/* 友谊奖励 */}
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medieval text-herb-green mb-2">友谊奖励</h3>
                    <div className="space-y-3">
                      {Object.entries(selectedNPC.friendship_rewards).map(([threshold, reward]) => {
                        const currentLevel = getFriendshipLevel(selectedNPC.id)
                        const isUnlocked = currentLevel >= parseInt(threshold)
                        
                        return (
                          <div
                            key={threshold}
                            className={`p-3 rounded-lg border ${
                              isUnlocked
                                ? 'bg-green-50 border-green-200'
                                : 'bg-sage-gray/10 border-sage-gray/30'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium text-herb-green">
                                {threshold}级: {reward.name}
                              </span>
                              {isUnlocked ? (
                                <Star className="text-herb-gold" size={16} />
                              ) : (
                                <Lock className="text-sage-gray" size={16} />
                              )}
                            </div>
                            <p className="text-sm text-sage-gray">{reward.effect}</p>
                          </div>
                        )
                      })}
                    </div>
                  </div>

                  {/* 互动选项 */}
                  <div>
                    <h3 className="text-lg font-medieval text-herb-green mb-2">互动选项</h3>
                    <div className="space-y-2">
                      <button
                        onClick={() => interactWithNPC(selectedNPC.id, 'talk')}
                        className="w-full flex items-center space-x-2 p-3 bg-herb-green/20 text-herb-green rounded-lg hover:bg-herb-green/30 transition-colors duration-200"
                      >
                        <MessageCircle size={16} />
                        <span>对话 (+5 友谊度)</span>
                      </button>
                      <button
                        onClick={() => interactWithNPC(selectedNPC.id, 'gift')}
                        className="w-full flex items-center space-x-2 p-3 bg-herb-gold/20 text-herb-gold rounded-lg hover:bg-herb-gold/30 transition-colors duration-200"
                      >
                        <Gift size={16} />
                        <span>送礼 (+15 友谊度)</span>
                      </button>
                      <button
                        onClick={() => interactWithNPC(selectedNPC.id, 'trade')}
                        className="w-full flex items-center space-x-2 p-3 bg-rosemary-purple/20 text-rosemary-purple rounded-lg hover:bg-rosemary-purple/30 transition-colors duration-200"
                      >
                        <Star size={16} />
                        <span>交易 (+10 友谊度)</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* 背景故事 */}
              <div className="mt-6 p-4 bg-herb-green/10 rounded-lg">
                <h3 className="text-lg font-medieval text-herb-green mb-2">背景故事</h3>
                <p className="text-sage-gray text-sm leading-relaxed whitespace-pre-line">
                  {selectedNPC.backstory}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default NPCRelationshipPanel
