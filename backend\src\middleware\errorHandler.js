// 全局错误处理中间件
export const errorHandler = (err, req, res, next) => {
  console.error('错误详情:', err)

  // 默认错误响应
  let error = {
    message: err.message || '服务器内部错误',
    status: err.status || 500
  }

  // Supabase 错误处理
  if (err.code) {
    switch (err.code) {
      case 'PGRST116':
        error = {
          message: '请求的资源不存在',
          status: 404
        }
        break
      case '23505':
        error = {
          message: '数据已存在，请检查唯一性约束',
          status: 409
        }
        break
      case '23503':
        error = {
          message: '关联数据不存在',
          status: 400
        }
        break
      case '42501':
        error = {
          message: '权限不足',
          status: 403
        }
        break
      default:
        error = {
          message: '数据库操作失败',
          status: 500
        }
    }
  }

  // JWT 错误处理
  if (err.name === 'JsonWebTokenError') {
    error = {
      message: '无效的访问令牌',
      status: 401
    }
  }

  if (err.name === 'TokenExpiredError') {
    error = {
      message: '访问令牌已过期',
      status: 401
    }
  }

  // Joi 验证错误
  if (err.isJoi) {
    error = {
      message: '请求参数验证失败',
      status: 400,
      details: err.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    }
  }

  // Stripe 错误处理
  if (err.type && err.type.startsWith('Stripe')) {
    error = {
      message: '支付处理失败',
      status: 400,
      details: err.message
    }
  }

  // 开发环境下返回完整错误信息
  if (process.env.NODE_ENV === 'development') {
    error.stack = err.stack
    error.originalError = err
  }

  res.status(error.status).json({
    success: false,
    error: error.message,
    ...(error.details && { details: error.details }),
    ...(error.stack && { stack: error.stack }),
    timestamp: new Date().toISOString()
  })
}

// 异步错误处理包装器
export const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// 404 错误处理
export const notFound = (req, res, next) => {
  const error = new Error(`路径不存在 - ${req.originalUrl}`)
  error.status = 404
  next(error)
}
