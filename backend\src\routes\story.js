import express from 'express'
import { authMiddleware, optionalAuth } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { supabase } from '../services/supabase.js'
import { storySystem } from '../services/storySystem.js'

const router = express.Router()

// 获取用户剧情进度
router.get('/progress', authMiddleware, asyncHandler(async (req, res) => {
  const availableStories = await storySystem.checkStoryProgress(req.user.id)
  
  // 获取用户已完成的章节
  const { data: completedChapters } = await supabase
    .from('user_story_progress')
    .select('*')
    .eq('user_id', req.user.id)

  // 组织数据结构
  const storiesMap = new Map()
  
  availableStories.forEach(story => {
    if (!storiesMap.has(story.story_id)) {
      storiesMap.set(story.story_id, {
        story_id: story.story_id,
        title: story.title || story.story_id,
        type: story.type,
        description: story.description || '',
        chapters: [],
        progress: 0
      })
    }
    
    const storyData = storiesMap.get(story.story_id)
    const isCompleted = completedChapters?.some(c => 
      c.story_id === story.story_id && c.chapter_id === story.chapter_id
    )
    
    storyData.chapters.push({
      id: story.chapter_id,
      title: story.title,
      description: story.description,
      completed: isCompleted,
      unlocked: true // 如果在availableStories中，说明已解锁
    })
  })

  // 计算每个剧情的完成度
  storiesMap.forEach(story => {
    const completedCount = story.chapters.filter(c => c.completed).length
    story.progress = story.chapters.length > 0 
      ? Math.round((completedCount / story.chapters.length) * 100) 
      : 0
  })

  res.json({
    success: true,
    data: Array.from(storiesMap.values())
  })
}))

// 开始章节
router.post('/:storyId/chapters/:chapterId/start', authMiddleware, asyncHandler(async (req, res) => {
  const { storyId, chapterId } = req.params
  
  // 检查章节是否可以开始
  const availableStories = await storySystem.checkStoryProgress(req.user.id)
  const chapter = availableStories.find(s => 
    s.story_id === storyId && s.chapter_id === chapterId
  )
  
  if (!chapter) {
    return res.status(400).json({
      success: false,
      error: '章节不可用或未解锁'
    })
  }

  // 记录章节开始
  await supabase
    .from('user_story_sessions')
    .insert({
      user_id: req.user.id,
      story_id: storyId,
      chapter_id: chapterId,
      started_at: new Date().toISOString()
    })

  res.json({
    success: true,
    data: {
      story_id: storyId,
      chapter_id: chapterId,
      chapter_data: chapter
    }
  })
}))

// 完成章节
router.post('/:storyId/chapters/:chapterId/complete', authMiddleware, asyncHandler(async (req, res) => {
  const { storyId, chapterId } = req.params
  
  const result = await storySystem.completeChapter(req.user.id, storyId, chapterId)
  
  if (result.success) {
    res.json({
      success: true,
      rewards: result.rewards,
      next_chapter: result.next_chapter,
      message: '章节完成！'
    })
  } else {
    res.status(400).json({
      success: false,
      error: result.error
    })
  }
}))

// 获取剧情详情
router.get('/:storyId', optionalAuth, asyncHandler(async (req, res) => {
  const { storyId } = req.params
  
  // 从剧情系统获取剧情信息
  const story = storySystem.mainStorylines.get(storyId) || 
                storySystem.hiddenStorylines.get(storyId)
  
  if (!story) {
    return res.status(404).json({
      success: false,
      error: '剧情不存在'
    })
  }

  // 如果用户已登录，获取进度信息
  let userProgress = null
  if (req.user) {
    const { data: progress } = await supabase
      .from('user_story_progress')
      .select('*')
      .eq('user_id', req.user.id)
      .eq('story_id', storyId)
    
    userProgress = progress
  }

  res.json({
    success: true,
    data: {
      ...story,
      user_progress: userProgress
    }
  })
}))

// 触发特殊事件
router.post('/trigger-event', authMiddleware, asyncHandler(async (req, res) => {
  const { event_type, context } = req.body
  
  let triggered = false
  let eventData = null

  switch (event_type) {
    case 'morning_mist_diary':
      // 检查是否触发晨雾行者诗篇
      if (Math.random() < 0.05) { // 5% 概率
        triggered = true
        eventData = {
          type: 'morning_mist_walker',
          title: '晨雾行者的呼唤',
          description: '您的诗篇触发了古老的共鸣...',
          rewards: { xp: 500, coins: 1000 }
        }
        
        // 解锁晨雾行者徽章
        await supabase
          .from('users')
          .update({
            badges: supabase.raw(`
              CASE 
                WHEN badges @> '[{"id": "morning_mist"}]'::jsonb 
                THEN badges 
                ELSE badges || '[{"id": "morning_mist", "name": "晨雾行者", "unlocked_at": "' + NOW() + '"}]'::jsonb
              END
            `)
          })
          .eq('id', req.user.id)
      }
      break
      
    case 'herb_collection':
      // 检查是否收集了所有四种草本
      const requiredHerbs = ['芜荽', '鼠尾草', '迷迭香', '百里香']
      const { data: userOrders } = await supabase
        .from('orders')
        .select(`
          products (name)
        `)
        .eq('user_id', req.user.id)
        .eq('payment_status', 'completed')
      
      const collectedHerbs = new Set()
      userOrders?.forEach(order => {
        if (requiredHerbs.includes(order.products?.name)) {
          collectedHerbs.add(order.products.name)
        }
      })
      
      if (collectedHerbs.size >= 4) {
        triggered = true
        eventData = {
          type: 'herb_master',
          title: '草本大师',
          description: '您已收集了所有四种经典草本，古老的知识向您敞开...',
          rewards: { xp: 300, coins: 500, badge: 'herb_sage' }
        }
      }
      break
      
    case 'midnight_exploration':
      // 深夜探索特殊事件
      const hour = new Date().getHours()
      if (hour >= 0 && hour <= 5) {
        triggered = true
        eventData = {
          type: 'night_secrets',
          title: '夜之秘密',
          description: '在深夜的探索中，您发现了隐藏的秘密...',
          rewards: { xp: 200, coins: 300 }
        }
      }
      break
  }

  if (triggered && eventData) {
    // 给予奖励
    if (eventData.rewards.xp) {
      await supabase.rpc('add_user_xp', {
        user_id: req.user.id,
        xp_amount: eventData.rewards.xp
      })
    }
    
    if (eventData.rewards.coins) {
      await supabase.rpc('add_vanilla_coins', {
        user_id: req.user.id,
        coin_amount: eventData.rewards.coins
      })
    }
    
    // 记录事件
    await supabase
      .from('story_events')
      .insert({
        user_id: req.user.id,
        event_type: eventData.type,
        event_data: eventData,
        triggered_at: new Date().toISOString()
      })
    
    // 发送通知
    await supabase
      .from('messages')
      .insert({
        sender_id: req.user.id,
        recipient_id: req.user.id,
        title: eventData.title,
        content: eventData.description,
        type: 'story_event'
      })
  }

  res.json({
    success: true,
    triggered,
    event_data: eventData
  })
}))

// 获取彩蛋和隐藏内容
router.get('/easter-eggs', authMiddleware, asyncHandler(async (req, res) => {
  const { data: userProfile } = await supabase
    .from('users')
    .select('level, badges, vanilla_coins')
    .eq('id', req.user.id)
    .single()

  const easterEggs = []

  // 检查各种彩蛋条件
  
  // 1. 等级彩蛋
  if (userProfile.level >= 50) {
    easterEggs.push({
      id: 'master_trader',
      title: '传奇商人',
      description: '达到50级的传奇商人，集市的真正主人',
      type: 'achievement',
      unlocked: true
    })
  }

  // 2. 财富彩蛋
  if (userProfile.vanilla_coins >= 10000) {
    easterEggs.push({
      id: 'coin_master',
      title: '香草币大师',
      description: '拥有超过10000香草币的富豪',
      type: 'wealth',
      unlocked: true
    })
  }

  // 3. 徽章收集彩蛋
  const badgeCount = userProfile.badges?.length || 0
  if (badgeCount >= 10) {
    easterEggs.push({
      id: 'badge_collector',
      title: '徽章收藏家',
      description: '收集了10个或更多徽章的收藏家',
      type: 'collection',
      unlocked: true
    })
  }

  // 4. 时间相关彩蛋
  const currentHour = new Date().getHours()
  if (currentHour >= 3 && currentHour <= 5) {
    easterEggs.push({
      id: 'dawn_visitor',
      title: '黎明访客',
      description: '在黎明时分访问集市的神秘访客',
      type: 'time',
      unlocked: true,
      temporary: true
    })
  }

  // 5. 特殊日期彩蛋
  const today = new Date()
  const isSpecialDate = (today.getMonth() === 3 && today.getDate() === 1) || // 愚人节
                       (today.getMonth() === 11 && today.getDate() === 25)   // 圣诞节
  
  if (isSpecialDate) {
    easterEggs.push({
      id: 'special_day',
      title: '特殊日子',
      description: '在特殊的日子里发现的秘密',
      type: 'date',
      unlocked: true,
      temporary: true
    })
  }

  res.json({
    success: true,
    data: easterEggs
  })
}))

// NPC 友谊度系统
router.post('/npc/:npcId/interact', authMiddleware, asyncHandler(async (req, res) => {
  const { npcId } = req.params
  const { interaction_type } = req.body

  // 获取当前友谊度
  const { data: friendship } = await supabase
    .from('npc_friendships')
    .select('*')
    .eq('user_id', req.user.id)
    .eq('npc_id', npcId)
    .single()

  let currentLevel = friendship?.friendship_level || 0
  let newLevel = currentLevel

  // 根据互动类型增加友谊度
  switch (interaction_type) {
    case 'talk':
      newLevel += 5
      break
    case 'gift':
      newLevel += 15
      break
    case 'quest_complete':
      newLevel += 25
      break
    case 'trade':
      newLevel += 10
      break
  }

  // 更新或创建友谊记录
  if (friendship) {
    await supabase
      .from('npc_friendships')
      .update({
        friendship_level: newLevel,
        last_interaction: new Date().toISOString()
      })
      .eq('user_id', req.user.id)
      .eq('npc_id', npcId)
  } else {
    await supabase
      .from('npc_friendships')
      .insert({
        user_id: req.user.id,
        npc_id: npcId,
        friendship_level: newLevel,
        last_interaction: new Date().toISOString()
      })
  }

  // 检查是否解锁新的剧情
  const unlockedStories = []
  if (newLevel >= 50 && currentLevel < 50) {
    // 解锁NPC个人故事
    unlockedStories.push(`${npcId}_personal_story`)
  }

  res.json({
    success: true,
    data: {
      npc_id: npcId,
      old_level: currentLevel,
      new_level: newLevel,
      unlocked_stories: unlockedStories
    },
    message: `与${npcId}的友谊度提升到${newLevel}！`
  })
}))

// 获取用户的NPC友谊度
router.get('/npc/friendships', authMiddleware, asyncHandler(async (req, res) => {
  const { data: friendships } = await supabase
    .from('npc_friendships')
    .select('*')
    .eq('user_id', req.user.id)

  const npcData = {
    lyrick: { name: '莱瑞克', role: '游吟诗人', max_level: 100 },
    flora: { name: '芙萝拉', role: '草本商人', max_level: 100 },
    kane: { name: '凯恩', role: '伶仃猎手', max_level: 100 }
  }

  const result = Object.entries(npcData).map(([npcId, npcInfo]) => {
    const friendship = friendships?.find(f => f.npc_id === npcId)
    return {
      npc_id: npcId,
      ...npcInfo,
      friendship_level: friendship?.friendship_level || 0,
      last_interaction: friendship?.last_interaction,
      progress: Math.round(((friendship?.friendship_level || 0) / npcInfo.max_level) * 100)
    }
  })

  res.json({
    success: true,
    data: result
  })
}))

export default router
