import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'
import { MapPin, Compass, Star, Lock, Unlock } from 'lucide-react'
import 'leaflet/dist/leaflet.css'

const MapPage = ({ user }) => {
  const [locations, setLocations] = useState([])
  const [userLocation, setUserLocation] = useState([39.9042, 116.4074]) // 默认北京
  const [unlockedLocations, setUnlockedLocations] = useState([])
  const [selectedLocation, setSelectedLocation] = useState(null)

  // 预定义的集市地点
  const marketLocations = [
    {
      id: 'scarborough',
      name: '斯卡布罗集市',
      description: '主要的集市广场，商户云集',
      coordinates: [39.9042, 116.4074],
      type: 'market',
      unlocked: true,
      merchants: 15,
      specialItems: ['香草', '古董', '手工艺品']
    },
    {
      id: 'solitude',
      name: '独孤城',
      description: '神秘的古城，隐藏着珍贵的宝物',
      coordinates: [39.9142, 116.4174],
      type: 'city',
      unlocked: false,
      unlockCondition: '达到10级',
      merchants: 5,
      specialItems: ['稀有装备', '古籍']
    },
    {
      id: 'forgotten_lands',
      name: '遗忘之地',
      description: '被时间遗忘的土地，充满未知的机遇',
      coordinates: [39.8942, 116.3974],
      type: 'forgotten',
      unlocked: false,
      unlockCondition: '完成晨雾行者任务',
      merchants: 3,
      specialItems: ['神秘物品', '时光碎片']
    },
    {
      id: 'herb_garden',
      name: '草本花园',
      description: '芙萝拉的花园，各种草本植物的家园',
      coordinates: [39.9242, 116.4274],
      type: 'garden',
      unlocked: true,
      merchants: 8,
      specialItems: ['芜荽', '鼠尾草', '迷迭香', '百里香']
    },
    {
      id: 'hunters_lodge',
      name: '猎人小屋',
      description: '凯恩的据点，冒险者的聚集地',
      coordinates: [39.8842, 116.4374],
      type: 'lodge',
      unlocked: false,
      unlockCondition: '完成5个狩猎任务',
      merchants: 2,
      specialItems: ['狩猎装备', '野味']
    }
  ]

  useEffect(() => {
    setLocations(marketLocations)
    
    // 模拟用户解锁状态
    if (user) {
      const unlocked = marketLocations.filter(loc => 
        loc.unlocked || 
        (user.level >= 10 && loc.id === 'solitude') ||
        (user.badges?.some(badge => badge.id === 'morning_mist') && loc.id === 'forgotten_lands')
      )
      setUnlockedLocations(unlocked.map(loc => loc.id))
    }
  }, [user])

  const getLocationIcon = (type, unlocked) => {
    const baseClass = `w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg`
    
    if (!unlocked) {
      return `${baseClass} bg-sage-gray`
    }

    switch (type) {
      case 'market':
        return `${baseClass} bg-herb-green`
      case 'city':
        return `${baseClass} bg-rosemary-purple`
      case 'forgotten':
        return `${baseClass} bg-herb-gold`
      case 'garden':
        return `${baseClass} bg-green-500`
      case 'lodge':
        return `${baseClass} bg-amber-600`
      default:
        return `${baseClass} bg-sage-gray`
    }
  }

  const LocationCard = ({ location }) => {
    const isUnlocked = unlockedLocations.includes(location.id)
    
    return (
      <div className={`card-medieval ${!isUnlocked ? 'opacity-60' : ''}`}>
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={getLocationIcon(location.type, isUnlocked)}>
              {isUnlocked ? <MapPin size={16} /> : <Lock size={16} />}
            </div>
            <div>
              <h3 className="font-medieval text-lg text-herb-green">{location.name}</h3>
              <p className="text-sm text-sage-gray">{location.description}</p>
            </div>
          </div>
          {isUnlocked ? (
            <Unlock size={16} className="text-herb-green" />
          ) : (
            <Lock size={16} className="text-sage-gray" />
          )}
        </div>

        {isUnlocked ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-sage-gray">商户数量:</span>
              <span className="font-medium text-herb-green">{location.merchants}</span>
            </div>
            
            <div>
              <p className="text-sm text-sage-gray mb-2">特色商品:</p>
              <div className="flex flex-wrap gap-2">
                {location.specialItems.map((item, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-herb-green/20 text-herb-green text-xs rounded"
                  >
                    {item}
                  </span>
                ))}
              </div>
            </div>

            <button
              onClick={() => setSelectedLocation(location)}
              className="w-full bg-herb-green hover:bg-rosemary-purple text-parchment py-2 rounded-lg transition-colors duration-200"
            >
              前往探索
            </button>
          </div>
        ) : (
          <div className="text-center py-4">
            <Lock size={24} className="text-sage-gray mx-auto mb-2" />
            <p className="text-sm text-sage-gray">解锁条件:</p>
            <p className="text-sm font-medium text-herb-green">{location.unlockCondition}</p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-medieval text-herb-green mb-2">探索地图</h1>
        <p className="text-sage-gray">发现隐藏地点，解锁神秘奖励</p>
      </div>

      {/* Map Container */}
      <div className="card-medieval">
        <div className="h-96 rounded-lg overflow-hidden">
          <MapContainer
            center={userLocation}
            zoom={13}
            style={{ height: '100%', width: '100%' }}
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            />
            
            {locations.map((location) => {
              const isUnlocked = unlockedLocations.includes(location.id)
              return (
                <Marker
                  key={location.id}
                  position={location.coordinates}
                  opacity={isUnlocked ? 1 : 0.5}
                >
                  <Popup>
                    <div className="text-center">
                      <h3 className="font-medieval text-herb-green mb-1">{location.name}</h3>
                      <p className="text-sm text-sage-gray mb-2">{location.description}</p>
                      {isUnlocked ? (
                        <button
                          onClick={() => setSelectedLocation(location)}
                          className="bg-herb-green text-parchment px-3 py-1 rounded text-sm"
                        >
                          探索
                        </button>
                      ) : (
                        <p className="text-xs text-sage-gray">🔒 {location.unlockCondition}</p>
                      )}
                    </div>
                  </Popup>
                </Marker>
              )
            })}
          </MapContainer>
        </div>
      </div>

      {/* Location Cards */}
      <div>
        <h2 className="text-2xl font-medieval text-herb-green mb-6 text-center">
          已知地点
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {locations.map((location) => (
            <LocationCard key={location.id} location={location} />
          ))}
        </div>
      </div>

      {/* Exploration Stats */}
      {user && (
        <div className="card-medieval">
          <h3 className="text-xl font-medieval text-herb-green mb-4 text-center">
            探索统计
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-herb-green">
                {unlockedLocations.length}
              </div>
              <div className="text-sm text-sage-gray">已解锁地点</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-rosemary-purple">
                {locations.length - unlockedLocations.length}
              </div>
              <div className="text-sm text-sage-gray">待解锁地点</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-herb-gold">
                {Math.round((unlockedLocations.length / locations.length) * 100)}%
              </div>
              <div className="text-sm text-sage-gray">探索进度</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-sage-gray">
                {user.level || 1}
              </div>
              <div className="text-sm text-sage-gray">当前等级</div>
            </div>
          </div>
        </div>
      )}

      {/* Selected Location Modal */}
      {selectedLocation && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-parchment rounded-lg p-6 max-w-md w-full">
            <h3 className="text-xl font-medieval text-herb-green mb-4">
              {selectedLocation.name}
            </h3>
            <p className="text-sage-gray mb-4">{selectedLocation.description}</p>
            
            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="text-sage-gray">商户数量:</span>
                <span className="font-medium">{selectedLocation.merchants}</span>
              </div>
              <div>
                <span className="text-sage-gray">特色商品:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {selectedLocation.specialItems.map((item, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-herb-green/20 text-herb-green text-xs rounded"
                    >
                      {item}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={() => setSelectedLocation(null)}
                className="flex-1 px-4 py-2 border border-sage-gray rounded-lg hover:bg-sage-gray/20 transition-colors duration-200"
              >
                关闭
              </button>
              <button
                onClick={() => {
                  // TODO: 实际的探索逻辑
                  alert(`开始探索 ${selectedLocation.name}！`)
                  setSelectedLocation(null)
                }}
                className="flex-1 btn-medieval"
              >
                开始探索
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MapPage
