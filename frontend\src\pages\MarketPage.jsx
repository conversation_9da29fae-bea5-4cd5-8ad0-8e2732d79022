import React, { useState, useEffect } from 'react'
import { Search, Filter, Grid, List, Star, MapPin } from 'lucide-react'
import { productAPI } from '../services/supabase'

const MarketPage = ({ user }) => {
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    category: '',
    minPrice: '',
    maxPrice: '',
    location: ''
  })
  const [viewMode, setViewMode] = useState('grid')
  const [categories, setCategories] = useState([])

  useEffect(() => {
    loadProducts()
    loadCategories()
  }, [])

  const loadProducts = async () => {
    try {
      setLoading(true)
      const data = await productAPI.getRecommendations({ limit: 20 })
      setProducts(data)
    } catch (error) {
      console.error('加载商品失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await fetch('/api/products/categories/list')
      const result = await response.json()
      if (result.success) {
        setCategories(result.data)
      }
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  }

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      loadProducts()
      return
    }

    try {
      setLoading(true)
      const data = await productAPI.searchProducts(searchTerm, filters)
      setProducts(data)
    } catch (error) {
      console.error('搜索失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const ProductCard = ({ product }) => (
    <div className="card-medieval hover:scale-105 transition-transform duration-300">
      <div className="aspect-square bg-sage-gray/20 rounded-lg mb-4 flex items-center justify-center">
        <span className="text-4xl">📦</span>
      </div>
      <h3 className="font-medium text-herb-green mb-2">{product.name}</h3>
      <p className="text-sm text-sage-gray mb-2 line-clamp-2">{product.description}</p>
      
      <div className="flex items-center space-x-2 mb-2">
        <MapPin size={14} className="text-sage-gray" />
        <span className="text-sm text-sage-gray">{product.merchants?.location}</span>
      </div>
      
      <div className="flex items-center space-x-2 mb-3">
        <span className="text-sm text-sage-gray">by</span>
        <span className="text-sm font-medium text-herb-green">{product.merchants?.name}</span>
        <div className="flex items-center space-x-1">
          <Star size={12} className="text-herb-gold fill-current" />
          <span className="text-xs text-sage-gray">Lv.{product.merchants?.users?.level || 1}</span>
        </div>
      </div>
      
      <div className="flex items-center justify-between">
        <span className="text-xl font-bold text-herb-gold">¥{product.price}</span>
        <span className="text-xs bg-sage-gray/20 px-2 py-1 rounded">{product.category}</span>
      </div>
      
      <button className="w-full mt-4 bg-herb-green hover:bg-rosemary-purple text-parchment py-2 rounded-lg transition-colors duration-200">
        查看详情
      </button>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-medieval text-herb-green mb-2">集市广场</h1>
        <p className="text-sage-gray">发现独特商品，与商户直接交易</p>
      </div>

      {/* Search and Filters */}
      <div className="card-medieval">
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="flex-1 relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sage-gray" />
            <input
              type="text"
              placeholder="搜索商品..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="w-full pl-10 pr-4 py-2 border border-sage-gray rounded-lg focus:outline-none focus:ring-2 focus:ring-herb-green"
            />
          </div>
          <button
            onClick={handleSearch}
            className="bg-herb-green hover:bg-rosemary-purple text-parchment px-6 py-2 rounded-lg transition-colors duration-200"
          >
            搜索
          </button>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <select
            value={filters.category}
            onChange={(e) => setFilters({ ...filters, category: e.target.value })}
            className="border border-sage-gray rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-herb-green"
          >
            <option value="">所有分类</option>
            {categories.map((cat) => (
              <option key={cat.name} value={cat.name}>
                {cat.name} ({cat.count})
              </option>
            ))}
          </select>

          <input
            type="number"
            placeholder="最低价格"
            value={filters.minPrice}
            onChange={(e) => setFilters({ ...filters, minPrice: e.target.value })}
            className="border border-sage-gray rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-herb-green"
          />

          <input
            type="number"
            placeholder="最高价格"
            value={filters.maxPrice}
            onChange={(e) => setFilters({ ...filters, maxPrice: e.target.value })}
            className="border border-sage-gray rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-herb-green"
          />

          <input
            type="text"
            placeholder="地区"
            value={filters.location}
            onChange={(e) => setFilters({ ...filters, location: e.target.value })}
            className="border border-sage-gray rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-herb-green"
          />
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-sage-gray" />
            <span className="text-sm text-sage-gray">找到 {products.length} 件商品</span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${viewMode === 'grid' ? 'bg-herb-green text-parchment' : 'text-sage-gray hover:bg-sage-gray/20'}`}
            >
              <Grid size={16} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${viewMode === 'list' ? 'bg-herb-green text-parchment' : 'text-sage-gray hover:bg-sage-gray/20'}`}
            >
              <List size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-herb-green mx-auto mb-4"></div>
          <p className="text-sage-gray">加载商品中...</p>
        </div>
      ) : products.length > 0 ? (
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
            : 'grid-cols-1'
        }`}>
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-medieval text-herb-green mb-2">没有找到商品</h3>
          <p className="text-sage-gray">尝试调整搜索条件或浏览其他分类</p>
        </div>
      )}
    </div>
  )
}

export default MarketPage
