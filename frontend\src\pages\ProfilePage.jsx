import React, { useState, useEffect } from 'react'
import { User, Star, Coins, Award, Calendar, Edit, Save, X } from 'lucide-react'
import { userAPI } from '../services/supabase'

const ProfilePage = ({ user }) => {
  const [userProfile, setUserProfile] = useState(null)
  const [userStats, setUserStats] = useState(null)
  const [badges, setBadges] = useState([])
  const [loading, setLoading] = useState(true)
  const [editing, setEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    username: '',
    organization: '',
    avatar_url: ''
  })

  useEffect(() => {
    if (user) {
      loadUserData()
    }
  }, [user])

  const loadUserData = async () => {
    try {
      setLoading(true)
      const [profile, stats, userBadges] = await Promise.all([
        userAPI.getProfile(user.id),
        userAPI.getUserStats(user.id),
        userAPI.getBadges(user.id)
      ])
      
      setUserProfile(profile)
      setUserStats(stats)
      setBadges(userBadges)
      setEditForm({
        username: profile.username || '',
        organization: profile.organization || '',
        avatar_url: profile.avatar_url || ''
      })
    } catch (error) {
      console.error('加载用户数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveProfile = async () => {
    try {
      const updatedProfile = await userAPI.updateProfile(user.id, editForm)
      setUserProfile(updatedProfile)
      setEditing(false)
    } catch (error) {
      console.error('更新档案失败:', error)
      alert('更新失败，请重试')
    }
  }

  const calculateLevelProgress = (xp) => {
    const currentLevel = Math.floor(xp / 100) + 1
    const currentLevelXP = (currentLevel - 1) * 100
    const nextLevelXP = currentLevel * 100
    const progress = ((xp - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100
    return { currentLevel, progress, nextLevelXP }
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">👤</div>
        <h2 className="text-2xl font-medieval text-herb-green mb-4">个人档案</h2>
        <p className="text-sage-gray mb-6">登录后查看你的档案信息</p>
        <button
          onClick={() => supabase.auth.signInWithOAuth({ provider: 'google' })}
          className="btn-medieval"
        >
          立即登录
        </button>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-herb-green mx-auto mb-4"></div>
        <p className="text-sage-gray">加载档案中...</p>
      </div>
    )
  }

  const levelInfo = calculateLevelProgress(userProfile?.xp || 0)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-medieval text-herb-green mb-2">个人档案</h1>
        <p className="text-sage-gray">管理你的集市身份</p>
      </div>

      {/* Profile Card */}
      <div className="card-medieval">
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-20 h-20 bg-herb-green rounded-full flex items-center justify-center">
              {userProfile?.avatar_url ? (
                <img 
                  src={userProfile.avatar_url} 
                  alt="头像" 
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <span className="text-2xl text-parchment font-bold">
                  {userProfile?.username?.charAt(0) || 'U'}
                </span>
              )}
            </div>
            <div>
              {editing ? (
                <div className="space-y-2">
                  <input
                    type="text"
                    value={editForm.username}
                    onChange={(e) => setEditForm({ ...editForm, username: e.target.value })}
                    className="border border-sage-gray rounded px-3 py-1 focus:outline-none focus:ring-2 focus:ring-herb-green"
                    placeholder="用户名"
                  />
                  <input
                    type="text"
                    value={editForm.organization}
                    onChange={(e) => setEditForm({ ...editForm, organization: e.target.value })}
                    className="border border-sage-gray rounded px-3 py-1 focus:outline-none focus:ring-2 focus:ring-herb-green"
                    placeholder="组织/公司"
                  />
                </div>
              ) : (
                <>
                  <h2 className="text-2xl font-medieval text-herb-green">
                    {userProfile?.username || '流浪者'}
                  </h2>
                  <p className="text-sage-gray">{userProfile?.organization || '自由职业者'}</p>
                </>
              )}
              <div className="flex items-center space-x-2 mt-2">
                <Calendar size={16} className="text-sage-gray" />
                <span className="text-sm text-sage-gray">
                  加入于 {new Date(userProfile?.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {editing ? (
              <>
                <button
                  onClick={handleSaveProfile}
                  className="p-2 bg-herb-green text-parchment rounded hover:bg-rosemary-purple transition-colors duration-200"
                >
                  <Save size={16} />
                </button>
                <button
                  onClick={() => setEditing(false)}
                  className="p-2 bg-sage-gray text-parchment rounded hover:bg-sage-gray/80 transition-colors duration-200"
                >
                  <X size={16} />
                </button>
              </>
            ) : (
              <button
                onClick={() => setEditing(true)}
                className="p-2 bg-herb-green text-parchment rounded hover:bg-rosemary-purple transition-colors duration-200"
              >
                <Edit size={16} />
              </button>
            )}
          </div>
        </div>

        {/* Level Progress */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <Star size={20} className="text-herb-gold" />
              <span className="font-medieval text-lg text-herb-green">
                等级 {levelInfo.currentLevel}
              </span>
            </div>
            <span className="text-sm text-sage-gray">
              {userProfile?.xp || 0} / {levelInfo.nextLevelXP} XP
            </span>
          </div>
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${levelInfo.progress}%` }}
            ></div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-herb-green/10 rounded-lg">
            <Coins size={24} className="text-herb-gold mx-auto mb-2" />
            <div className="text-2xl font-bold text-herb-green">{userProfile?.vanilla_coins || 0}</div>
            <div className="text-sm text-sage-gray">香草币</div>
          </div>
          <div className="text-center p-4 bg-rosemary-purple/10 rounded-lg">
            <Award size={24} className="text-rosemary-purple mx-auto mb-2" />
            <div className="text-2xl font-bold text-rosemary-purple">{badges.length}</div>
            <div className="text-sm text-sage-gray">徽章</div>
          </div>
          <div className="text-center p-4 bg-herb-gold/10 rounded-lg">
            <User size={24} className="text-herb-gold mx-auto mb-2" />
            <div className="text-2xl font-bold text-herb-gold">{userStats?.totalOrders || 0}</div>
            <div className="text-sm text-sage-gray">订单数</div>
          </div>
          <div className="text-center p-4 bg-sage-gray/10 rounded-lg">
            <Star size={24} className="text-sage-gray mx-auto mb-2" />
            <div className="text-2xl font-bold text-sage-gray">{userStats?.completedTasks || 0}</div>
            <div className="text-sm text-sage-gray">完成任务</div>
          </div>
        </div>
      </div>

      {/* Badges Section */}
      <div className="card-medieval">
        <h3 className="text-xl font-medieval text-herb-green mb-4">徽章收藏</h3>
        {badges.length > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {badges.map((badge, index) => (
              <div key={index} className="text-center p-4 border border-sage-gray rounded-lg">
                <div className="text-3xl mb-2">🏆</div>
                <h4 className="font-medium text-herb-green mb-1">{badge.name}</h4>
                <p className="text-xs text-sage-gray">
                  {new Date(badge.unlocked_at).toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">🏆</div>
            <p className="text-sage-gray">还没有获得徽章</p>
            <p className="text-sm text-sage-gray mt-2">完成任务和探索来解锁徽章吧！</p>
          </div>
        )}
      </div>

      {/* Activity Summary */}
      <div className="card-medieval">
        <h3 className="text-xl font-medieval text-herb-green mb-4">活动概览</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-herb-green/5 rounded-lg">
            <span className="text-herb-green">总订单数</span>
            <span className="font-bold text-herb-green">{userStats?.totalOrders || 0}</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-rosemary-purple/5 rounded-lg">
            <span className="text-rosemary-purple">发布商品</span>
            <span className="font-bold text-rosemary-purple">{userStats?.totalProducts || 0}</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-herb-gold/5 rounded-lg">
            <span className="text-herb-gold">完成任务</span>
            <span className="font-bold text-herb-gold">{userStats?.completedTasks || 0}</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-sage-gray/5 rounded-lg">
            <span className="text-sage-gray">日记条目</span>
            <span className="font-bold text-sage-gray">{userStats?.diaryEntries || 0}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfilePage
