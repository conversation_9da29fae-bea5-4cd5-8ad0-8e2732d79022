import React, { useState, useEffect } from 'react'
import { Star, TrendingUp, Award, Sparkles } from 'lucide-react'
import { calculateLevelProgress, getLevelTitle } from '../utils/helpers'

const LevelProgress = ({ user, showDetails = false, compact = false }) => {
  const [isLevelingUp, setIsLevelingUp] = useState(false)
  const [previousLevel, setPreviousLevel] = useState(null)

  const levelInfo = calculateLevelProgress(user?.xp || 0)
  const levelTitle = getLevelTitle(levelInfo.currentLevel)

  useEffect(() => {
    // 检测升级动画
    if (previousLevel && levelInfo.currentLevel > previousLevel) {
      setIsLevelingUp(true)
      setTimeout(() => setIsLevelingUp(false), 3000)
    }
    setPreviousLevel(levelInfo.currentLevel)
  }, [levelInfo.currentLevel])

  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1">
          <Star size={16} className="text-herb-gold" />
          <span className="font-medium text-herb-green">Lv.{levelInfo.currentLevel}</span>
        </div>
        <div className="flex-1 bg-sage-gray/30 rounded-full h-2 min-w-[60px]">
          <div 
            className="bg-gradient-to-r from-herb-green to-herb-gold h-full rounded-full transition-all duration-500 ease-out"
            style={{ width: `${levelInfo.progress}%` }}
          />
        </div>
        <span className="text-xs text-sage-gray">{user?.xp || 0} XP</span>
      </div>
    )
  }

  return (
    <div className={`${showDetails ? 'card-medieval' : 'p-4 bg-herb-green/10 rounded-lg'}`}>
      {/* 升级动画 */}
      {isLevelingUp && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 pointer-events-none">
          <div className="text-center animate-pulse">
            <div className="text-6xl mb-4">🎉</div>
            <h2 className="text-3xl font-medieval text-herb-gold mb-2">升级了！</h2>
            <p className="text-xl text-parchment">
              恭喜达到 {levelInfo.currentLevel} 级！
            </p>
            <div className="flex items-center justify-center space-x-2 mt-4">
              <Sparkles className="text-herb-gold animate-spin" />
              <span className="text-parchment">{levelTitle}</span>
              <Sparkles className="text-herb-gold animate-spin" />
            </div>
          </div>
        </div>
      )}

      {/* 等级信息头部 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="w-12 h-12 bg-herb-green rounded-full flex items-center justify-center">
              <Star size={24} className="text-herb-gold" />
            </div>
            {isLevelingUp && (
              <div className="absolute inset-0 bg-herb-gold rounded-full animate-ping opacity-75" />
            )}
          </div>
          <div>
            <h3 className="text-lg font-medieval text-herb-green">
              等级 {levelInfo.currentLevel}
            </h3>
            <p className="text-sm text-sage-gray">{levelTitle}</p>
          </div>
        </div>
        
        {showDetails && (
          <div className="text-right">
            <div className="text-2xl font-bold text-herb-green">{user?.xp || 0}</div>
            <div className="text-xs text-sage-gray">总经验值</div>
          </div>
        )}
      </div>

      {/* 进度条 */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-sage-gray">升级进度</span>
          <span className="text-sm font-medium text-herb-green">
            {user?.xp || 0} / {levelInfo.nextLevelXP} XP
          </span>
        </div>
        
        <div className="relative">
          <div className="w-full bg-sage-gray/30 rounded-full h-3 overflow-hidden">
            <div 
              className="bg-gradient-to-r from-herb-green to-herb-gold h-full rounded-full transition-all duration-500 ease-out relative"
              style={{ width: `${levelInfo.progress}%` }}
            >
              {/* 进度条光效 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
            </div>
          </div>
          
          {/* 进度百分比 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs font-medium text-herb-green drop-shadow-sm">
              {Math.round(levelInfo.progress)}%
            </span>
          </div>
        </div>
        
        <div className="flex justify-between text-xs text-sage-gray mt-1">
          <span>还需 {levelInfo.xpToNext} XP 升级</span>
          <span>下一级: Lv.{levelInfo.currentLevel + 1}</span>
        </div>
      </div>

      {showDetails && (
        <>
          {/* 等级奖励预览 */}
          <div className="border-t border-sage-gray/30 pt-4">
            <h4 className="text-sm font-medium text-herb-green mb-3">升级奖励</h4>
            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center space-x-2 p-2 bg-herb-gold/10 rounded-lg">
                <div className="w-6 h-6 bg-herb-gold rounded-full flex items-center justify-center">
                  <span className="text-xs text-white">🪙</span>
                </div>
                <div>
                  <div className="text-sm font-medium text-herb-gold">+50 香草币</div>
                  <div className="text-xs text-sage-gray">升级奖励</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 p-2 bg-rosemary-purple/10 rounded-lg">
                <div className="w-6 h-6 bg-rosemary-purple rounded-full flex items-center justify-center">
                  <Award size={12} className="text-white" />
                </div>
                <div>
                  <div className="text-sm font-medium text-rosemary-purple">新徽章</div>
                  <div className="text-xs text-sage-gray">特定等级</div>
                </div>
              </div>
            </div>
          </div>

          {/* 等级里程碑 */}
          <div className="border-t border-sage-gray/30 pt-4 mt-4">
            <h4 className="text-sm font-medium text-herb-green mb-3">等级里程碑</h4>
            <div className="space-y-2">
              {[
                { level: 5, title: '新手商人', reward: '解锁商户功能', achieved: levelInfo.currentLevel >= 5 },
                { level: 10, title: '见习商人', reward: '解锁独孤城', achieved: levelInfo.currentLevel >= 10 },
                { level: 20, title: '熟练商人', reward: '解锁高级任务', achieved: levelInfo.currentLevel >= 20 },
                { level: 30, title: '专家级商人', reward: '解锁特殊商品', achieved: levelInfo.currentLevel >= 30 },
                { level: 50, title: '传奇商人', reward: '解锁传奇徽章', achieved: levelInfo.currentLevel >= 50 }
              ].map((milestone, index) => (
                <div 
                  key={index}
                  className={`flex items-center justify-between p-2 rounded-lg ${
                    milestone.achieved 
                      ? 'bg-green-100 text-green-700' 
                      : levelInfo.currentLevel === milestone.level - 1
                        ? 'bg-herb-gold/20 text-herb-gold'
                        : 'bg-sage-gray/10 text-sage-gray'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                      milestone.achieved ? 'bg-green-500 text-white' : 'bg-sage-gray text-white'
                    }`}>
                      {milestone.level}
                    </div>
                    <div>
                      <div className="text-sm font-medium">{milestone.title}</div>
                      <div className="text-xs opacity-75">{milestone.reward}</div>
                    </div>
                  </div>
                  {milestone.achieved && (
                    <div className="text-green-500">✓</div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 经验值获取提示 */}
          <div className="border-t border-sage-gray/30 pt-4 mt-4">
            <h4 className="text-sm font-medium text-herb-green mb-3">💡 获取经验值</h4>
            <div className="grid grid-cols-2 gap-2 text-xs text-sage-gray">
              <div>• 购买商品: +10 XP</div>
              <div>• 发布日记: +10 XP</div>
              <div>• 完成任务: +20-100 XP</div>
              <div>• 日记被点赞: +5 XP</div>
              <div>• 探索地点: +10-60 XP</div>
              <div>• 与NPC对话: +15-25 XP</div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default LevelProgress
