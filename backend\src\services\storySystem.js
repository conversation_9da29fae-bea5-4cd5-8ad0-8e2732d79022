import { supabase } from './supabase.js'

// 剧情系统
class StorySystem {
  constructor() {
    this.mainStorylines = new Map()
    this.hiddenStorylines = new Map()
    this.initializeStories()
  }

  // 初始化剧情线
  initializeStories() {
    // 主线剧情：斯卡布罗集市的传承
    this.mainStorylines.set('scarborough_legacy', {
      id: 'scarborough_legacy',
      title: '斯卡布罗集市的传承',
      description: '探索古老集市的秘密，成为真正的传承者',
      chapters: [
        {
          id: 'arrival',
          title: '初来乍到',
          description: '你刚刚来到斯卡布罗集市，一切都是那么新奇',
          trigger_conditions: { user_level: 1 },
          completion_conditions: { 
            actions: ['complete_first_purchase', 'talk_to_lyrick'] 
          },
          rewards: { xp: 100, coins: 200 },
          npcs: ['lyrick'],
          locations: ['scarborough'],
          story_text: `
            微风轻抚过古老的石板路，你踏进了传说中的斯卡布罗集市。
            空气中弥漫着芜荽、鼠尾草、迷迭香和百里香的香气，
            仿佛那首古老的歌谣在耳边轻柔地响起。
            
            一位身着华丽服装的游吟诗人注意到了你...
          `
        },
        {
          id: 'first_trade',
          title: '初次交易',
          description: '学会在集市中进行交易，体验商业的魅力',
          trigger_conditions: { 
            completed_chapters: ['arrival'],
            user_level: 3
          },
          completion_conditions: { 
            actions: ['complete_three_purchases', 'earn_first_coins'] 
          },
          rewards: { xp: 200, coins: 500, badge: 'first_trader' },
          npcs: ['flora'],
          story_text: `
            芙萝拉的草本花园里，阳光透过叶片洒下斑驳的光影。
            她温和地教导你如何识别不同草本的品质，
            如何与商户建立信任关系。
            
            "在斯卡布罗集市，每一次交易都不仅仅是商品的交换，
            更是故事和情感的传递。"她如是说。
          `
        },
        {
          id: 'merchant_path',
          title: '商户之路',
          description: '决定成为商户，开始你的经营之旅',
          trigger_conditions: { 
            completed_chapters: ['first_trade'],
            user_level: 5
          },
          completion_conditions: { 
            actions: ['become_merchant', 'sell_first_product'] 
          },
          rewards: { xp: 300, coins: 1000, badge: 'merchant_initiate' },
          npcs: ['kane'],
          story_text: `
            凯恩的猎人小屋里，火光摇曳。这位沉默寡言的猎手
            意外地给了你关于经营的建议。
            
            "生存不仅仅是狩猎，"他低沉地说，
            "有时候，最好的猎物是机会。"
          `
        },
        {
          id: 'community_bonds',
          title: '社区纽带',
          description: '与其他商户建立联系，融入集市社区',
          trigger_conditions: { 
            completed_chapters: ['merchant_path'],
            user_level: 10
          },
          completion_conditions: { 
            actions: ['help_other_merchants', 'participate_in_event'] 
          },
          rewards: { xp: 500, coins: 1500, badge: 'community_builder' },
          story_text: `
            集市的夜晚格外宁静，商户们聚在一起分享着彼此的故事。
            你发现，这里不仅仅是一个交易场所，
            更是一个充满温情的大家庭。
            
            每个人都有自己的故事，每个故事都值得被倾听。
          `
        },
        {
          id: 'ancient_secrets',
          title: '古老的秘密',
          description: '发现集市背后隐藏的古老秘密',
          trigger_conditions: { 
            completed_chapters: ['community_bonds'],
            user_level: 20,
            special_conditions: ['unlock_solitude']
          },
          completion_conditions: { 
            actions: ['explore_forgotten_lands', 'find_ancient_artifact'] 
          },
          rewards: { xp: 1000, coins: 3000, badge: 'secret_keeper' },
          story_text: `
            在独孤城的废墟中，你发现了一块古老的石碑。
            上面刻着模糊的文字，似乎在诉说着集市的起源。
            
            "那些寻找真相的人，终将找到属于自己的答案。"
            石碑上的文字在月光下闪闪发光。
          `
        }
      ]
    })

    // 隐藏剧情线：晨雾行者的传说
    this.hiddenStorylines.set('morning_mist_walker', {
      id: 'morning_mist_walker',
      title: '晨雾行者的传说',
      description: '追寻神秘的晨雾行者足迹，解开古老的谜团',
      hidden: true,
      trigger_conditions: { 
        special_diary: 'morning_mist',
        user_level: 15
      },
      chapters: [
        {
          id: 'first_glimpse',
          title: '初见晨雾',
          description: '你的诗篇触发了古老的共鸣',
          trigger_conditions: { 
            actions: ['write_morning_mist_diary'] 
          },
          completion_conditions: { 
            actions: ['visit_dawn_market', 'collect_morning_dew'] 
          },
          rewards: { xp: 500, coins: 1000, special_item: 'morning_mist_pendant' },
          story_text: `
            当你写下那篇充满诗意的日记时，
            整个集市似乎都在那一瞬间安静了下来。
            
            晨雾缓缓升起，在你眼前勾勒出一个模糊的身影...
            那是传说中的晨雾行者吗？
          `
        },
        {
          id: 'following_traces',
          title: '追寻足迹',
          description: '跟随晨雾行者留下的线索',
          trigger_conditions: { 
            completed_chapters: ['first_glimpse'],
            time_condition: 'dawn'
          },
          completion_conditions: { 
            actions: ['solve_mist_riddle', 'find_hidden_path'] 
          },
          rewards: { xp: 800, coins: 2000, special_ability: 'mist_sight' },
          story_text: `
            在黎明的薄雾中，你看到了只有少数人能看到的景象：
            一条隐秘的小径，通向集市深处的秘密花园。
            
            脚步声在雾中回响，但当你转身时，
            却只看到逐渐消散的雾气...
          `
        },
        {
          id: 'the_revelation',
          title: '真相大白',
          description: '揭开晨雾行者的真实身份',
          trigger_conditions: { 
            completed_chapters: ['following_traces'],
            special_conditions: ['collect_all_herbs', 'master_level_reached']
          },
          completion_conditions: { 
            actions: ['confront_the_truth', 'make_final_choice'] 
          },
          rewards: { 
            xp: 2000, 
            coins: 5000, 
            badge: 'morning_mist_walker',
            special_ending: true
          },
          story_text: `
            在遗忘之地的最深处，你终于见到了晨雾行者的真面目。
            
            "你已经走了很远的路，"一个熟悉的声音说道，
            "现在，你必须做出选择：
            是继续作为一个普通的商户，
            还是成为新的晨雾行者，守护这片土地的秘密？"
          `
        }
      ]
    })

    // 支线剧情：NPC个人故事
    this.initializeNPCStories()
  }

  // 初始化NPC个人故事
  initializeNPCStories() {
    // 莱瑞克的故事
    this.hiddenStorylines.set('lyrick_tale', {
      id: 'lyrick_tale',
      title: '游吟诗人的秘密',
      description: '了解莱瑞克的过去和他守护的秘密',
      hidden: true,
      trigger_conditions: { 
        npc_friendship: { lyrick: 50 },
        user_level: 12
      },
      chapters: [
        {
          id: 'poets_burden',
          title: '诗人的负担',
          story_text: `
            "你知道吗，"莱瑞克在一个安静的夜晚对你说，
            "我不仅仅是一个游吟诗人。我是这个集市记忆的守护者。
            
            每一首歌，每一个故事，都承载着这里的历史。
            但有些记忆...太过沉重。"
          `
        }
      ]
    })

    // 芙萝拉的故事
    this.hiddenStorylines.set('flora_secret', {
      id: 'flora_secret',
      title: '花园的守护者',
      description: '芙萝拉与神秘花园的联系',
      hidden: true,
      trigger_conditions: { 
        npc_friendship: { flora: 60 },
        actions: ['collect_rare_herbs']
      }
    })

    // 凯恩的故事
    this.hiddenStorylines.set('kane_mystery', {
      id: 'kane_mystery',
      title: '猎手的过往',
      description: '凯恩为什么选择独居，他在寻找什么？',
      hidden: true,
      trigger_conditions: { 
        npc_friendship: { kane: 40 },
        user_level: 18,
        time_condition: 'night'
      }
    })
  }

  // 检查用户的剧情进度
  async checkStoryProgress(userId) {
    try {
      const { data: userProgress } = await supabase
        .from('user_story_progress')
        .select('*')
        .eq('user_id', userId)

      const { data: userProfile } = await supabase
        .from('users')
        .select('level, badges')
        .eq('id', userId)
        .single()

      const availableStories = []
      const completedChapters = userProgress?.map(p => p.chapter_id) || []

      // 检查主线剧情
      for (const [storyId, story] of this.mainStorylines) {
        for (const chapter of story.chapters) {
          if (this.canTriggerChapter(chapter, userProfile, completedChapters)) {
            availableStories.push({
              story_id: storyId,
              chapter_id: chapter.id,
              type: 'main',
              ...chapter
            })
          }
        }
      }

      // 检查隐藏剧情
      for (const [storyId, story] of this.hiddenStorylines) {
        if (this.canTriggerStory(story, userProfile)) {
          for (const chapter of story.chapters || []) {
            if (this.canTriggerChapter(chapter, userProfile, completedChapters)) {
              availableStories.push({
                story_id: storyId,
                chapter_id: chapter.id,
                type: 'hidden',
                ...chapter
              })
            }
          }
        }
      }

      return availableStories
    } catch (error) {
      console.error('检查剧情进度失败:', error)
      return []
    }
  }

  // 检查是否可以触发剧情
  canTriggerStory(story, userProfile) {
    const conditions = story.trigger_conditions

    if (conditions.user_level && userProfile.level < conditions.user_level) {
      return false
    }

    if (conditions.special_diary && !this.hasSpecialDiary(userProfile, conditions.special_diary)) {
      return false
    }

    return true
  }

  // 检查是否可以触发章节
  canTriggerChapter(chapter, userProfile, completedChapters) {
    const conditions = chapter.trigger_conditions

    if (conditions.user_level && userProfile.level < conditions.user_level) {
      return false
    }

    if (conditions.completed_chapters) {
      for (const requiredChapter of conditions.completed_chapters) {
        if (!completedChapters.includes(requiredChapter)) {
          return false
        }
      }
    }

    return true
  }

  // 检查是否有特殊日记
  hasSpecialDiary(userProfile, diaryType) {
    // 这里应该检查用户是否写过特定类型的日记
    return userProfile.badges?.some(badge => badge.id === 'morning_mist')
  }

  // 完成章节
  async completeChapter(userId, storyId, chapterId) {
    try {
      const story = this.mainStorylines.get(storyId) || this.hiddenStorylines.get(storyId)
      if (!story) return { success: false, error: '剧情不存在' }

      const chapter = story.chapters.find(c => c.id === chapterId)
      if (!chapter) return { success: false, error: '章节不存在' }

      // 记录完成进度
      await supabase
        .from('user_story_progress')
        .insert({
          user_id: userId,
          story_id: storyId,
          chapter_id: chapterId,
          completed_at: new Date().toISOString()
        })

      // 给予奖励
      if (chapter.rewards) {
        if (chapter.rewards.xp) {
          await supabase.rpc('add_user_xp', {
            user_id: userId,
            xp_amount: chapter.rewards.xp
          })
        }

        if (chapter.rewards.coins) {
          await supabase.rpc('add_vanilla_coins', {
            user_id: userId,
            coin_amount: chapter.rewards.coins
          })
        }

        if (chapter.rewards.badge) {
          await this.unlockBadge(userId, chapter.rewards.badge)
        }
      }

      // 发送完成通知
      await supabase
        .from('messages')
        .insert({
          sender_id: userId,
          recipient_id: userId,
          title: '剧情章节完成',
          content: `恭喜完成章节：${chapter.title}！`,
          type: 'story'
        })

      return { 
        success: true, 
        rewards: chapter.rewards,
        next_chapter: this.getNextChapter(story, chapterId)
      }
    } catch (error) {
      console.error('完成章节失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 获取下一章节
  getNextChapter(story, currentChapterId) {
    const currentIndex = story.chapters.findIndex(c => c.id === currentChapterId)
    if (currentIndex >= 0 && currentIndex < story.chapters.length - 1) {
      return story.chapters[currentIndex + 1]
    }
    return null
  }

  // 解锁徽章
  async unlockBadge(userId, badgeId) {
    const badge = {
      id: badgeId,
      name: this.getBadgeName(badgeId),
      unlocked_at: new Date().toISOString()
    }

    await supabase
      .from('users')
      .update({
        badges: supabase.raw(`badges || '[${JSON.stringify(badge)}]'::jsonb`)
      })
      .eq('id', userId)
  }

  // 获取徽章名称
  getBadgeName(badgeId) {
    const badgeNames = {
      first_trader: '初级商人',
      merchant_initiate: '商户学徒',
      community_builder: '社区建设者',
      secret_keeper: '秘密守护者',
      morning_mist_walker: '晨雾行者'
    }
    return badgeNames[badgeId] || badgeId
  }

  // 触发随机事件
  async triggerRandomEvent(userId) {
    const randomEvents = [
      {
        id: 'mysterious_merchant',
        title: '神秘商人',
        description: '一个神秘的商人出现在集市中，带来了稀有的商品',
        probability: 0.05,
        rewards: { special_item: 'mystery_box' }
      },
      {
        id: 'herb_blessing',
        title: '草本祝福',
        description: '芙萝拉的花园散发出神奇的光芒，所有草本都得到了祝福',
        probability: 0.1,
        rewards: { xp: 100, coins: 200 }
      },
      {
        id: 'poets_inspiration',
        title: '诗人的灵感',
        description: '莱瑞克的歌声激发了你的创作灵感',
        probability: 0.08,
        rewards: { diary_bonus: true }
      }
    ]

    for (const event of randomEvents) {
      if (Math.random() < event.probability) {
        await this.executeRandomEvent(userId, event)
        return event
      }
    }

    return null
  }

  // 执行随机事件
  async executeRandomEvent(userId, event) {
    // 给予奖励
    if (event.rewards.xp) {
      await supabase.rpc('add_user_xp', {
        user_id: userId,
        xp_amount: event.rewards.xp
      })
    }

    if (event.rewards.coins) {
      await supabase.rpc('add_vanilla_coins', {
        user_id: userId,
        coin_amount: event.rewards.coins
      })
    }

    // 发送事件通知
    await supabase
      .from('messages')
      .insert({
        sender_id: userId,
        recipient_id: userId,
        title: event.title,
        content: event.description,
        type: 'event'
      })
  }
}

// 创建全局剧情系统实例
export const storySystem = new StorySystem()

export default StorySystem
