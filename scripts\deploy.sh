#!/bin/bash

# 斯卡布罗集市部署脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查部署依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查环境变量
check_env() {
    log_info "检查环境变量..."
    
    if [ ! -f .env ]; then
        log_error ".env 文件不存在，请复制 .env.example 并配置"
        exit 1
    fi
    
    # 检查必要的环境变量
    required_vars=(
        "SUPABASE_URL"
        "SUPABASE_ANON_KEY"
        "SUPABASE_SERVICE_KEY"
        "JWT_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" .env; then
            log_error "环境变量 ${var} 未配置"
            exit 1
        fi
    done
    
    log_success "环境变量检查完成"
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker build -t scarborough-frontend:latest ./frontend
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build -t scarborough-backend:latest ./backend
    
    log_success "镜像构建完成"
}

# 数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    
    # 检查 Supabase 连接
    if ! curl -s "${SUPABASE_URL}/rest/v1/" > /dev/null; then
        log_error "无法连接到 Supabase，请检查配置"
        exit 1
    fi
    
    # 执行 SQL 脚本
    log_info "执行数据库架构脚本..."
    # 这里可以添加具体的数据库迁移逻辑
    
    log_success "数据库迁移完成"
}

# 部署服务
deploy_services() {
    log_info "部署服务..."
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose down
    
    # 启动新服务
    log_info "启动新服务..."
    if [ "$1" = "production" ]; then
        docker-compose -f docker-compose.prod.yml up -d
    else
        docker-compose up -d
    fi
    
    log_success "服务部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 30
    
    # 检查前端服务
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务运行正常"
    else
        log_error "前端服务健康检查失败"
        return 1
    fi
    
    # 检查后端服务
    if curl -f http://localhost:5000/api/health > /dev/null 2>&1; then
        log_success "后端服务运行正常"
    else
        log_error "后端服务健康检查失败"
        return 1
    fi
    
    log_success "健康检查完成"
}

# 清理旧镜像
cleanup() {
    log_info "清理旧镜像..."
    
    # 删除悬空镜像
    docker image prune -f
    
    # 删除未使用的容器
    docker container prune -f
    
    log_success "清理完成"
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    # 创建备份目录
    backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份 Redis 数据
    if docker ps | grep -q redis; then
        docker exec redis redis-cli BGSAVE
        docker cp redis:/data/dump.rdb "$backup_dir/redis_dump.rdb"
        log_success "Redis 数据备份完成"
    fi
    
    # 备份配置文件
    cp .env "$backup_dir/"
    cp docker-compose.yml "$backup_dir/"
    
    log_success "数据备份完成: $backup_dir"
}

# 回滚部署
rollback() {
    log_warning "执行回滚..."
    
    # 停止当前服务
    docker-compose down
    
    # 恢复到上一个版本
    # 这里需要根据具体的版本管理策略来实现
    
    log_success "回滚完成"
}

# SSL 证书设置
setup_ssl() {
    log_info "设置 SSL 证书..."
    
    # 创建 SSL 目录
    mkdir -p nginx/ssl
    
    # 使用 Let's Encrypt 获取证书
    if command -v certbot &> /dev/null; then
        certbot certonly --webroot -w /var/www/certbot \
            -d scarborough-fair.com \
            -d www.scarborough-fair.com \
            --email <EMAIL> \
            --agree-tos \
            --non-interactive
        
        # 复制证书到 nginx 目录
        cp /etc/letsencrypt/live/scarborough-fair.com/fullchain.pem nginx/ssl/
        cp /etc/letsencrypt/live/scarborough-fair.com/privkey.pem nginx/ssl/
        
        log_success "SSL 证书设置完成"
    else
        log_warning "Certbot 未安装，跳过 SSL 证书设置"
    fi
}

# 监控设置
setup_monitoring() {
    log_info "设置监控..."
    
    # 创建监控配置目录
    mkdir -p monitoring/{prometheus,grafana/{dashboards,datasources}}
    
    # 复制监控配置文件
    # 这里可以添加具体的监控配置
    
    log_success "监控设置完成"
}

# 主函数
main() {
    local environment=${1:-development}
    local action=${2:-deploy}
    
    log_info "开始部署斯卡布罗集市 (环境: $environment, 操作: $action)"
    
    case $action in
        "deploy")
            check_dependencies
            check_env
            backup_data
            build_images
            migrate_database
            deploy_services $environment
            health_check
            cleanup
            ;;
        "rollback")
            rollback
            ;;
        "ssl")
            setup_ssl
            ;;
        "monitoring")
            setup_monitoring
            ;;
        "backup")
            backup_data
            ;;
        *)
            log_error "未知操作: $action"
            echo "用法: $0 [environment] [action]"
            echo "环境: development|production"
            echo "操作: deploy|rollback|ssl|monitoring|backup"
            exit 1
            ;;
    esac
    
    log_success "部署完成！"
}

# 执行主函数
main "$@"
