/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // 斯卡布罗集市色彩主题
        'herb-green': '#4A7043',      // 草绿色 (主色调)
        'sage-gray': '#B0B7A4',       // 鼠尾灰 (次要色)
        'herb-gold': '#D4A017',       // 草本金黄 (强调色)
        'rosemary-purple': '#6B4E71', // 迷迭香紫 (深色)
        'parchment': '#F5E8C7',       // 羊皮纸白 (背景色)
      },
      fontFamily: {
        'medieval': ['Cinzel', 'serif'],
        'body': ['Inter', 'sans-serif'],
      },
      animation: {
        'bounce-slow': 'bounce 2s infinite',
        'pulse-slow': 'pulse 3s infinite',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        }
      }
    },
  },
  plugins: [],
}
