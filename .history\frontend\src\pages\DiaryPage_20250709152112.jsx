import React, { useState, useEffect } from 'react'
import { PenTool, Heart, MessageCircle, Share, Calendar, Eye } from 'lucide-react'
import { diaryAPI } from '../services/supabase'

const DiaryPage = ({ user }) => {
  const [entries, setEntries] = useState([])
  const [loading, setLoading] = useState(true)
  const [showEditor, setShowEditor] = useState(false)
  const [newEntry, setNewEntry] = useState({
    title: '',
    content: '',
    type: 'vanilla_diary',
    visibility: 'public'
  })

  useEffect(() => {
    if (user) {
      loadDiaryEntries()
    }
  }, [user])

  const loadDiaryEntries = async () => {
    try {
      setLoading(true)
      const data = await diaryAPI.getDiaryEntries(user.id)
      setEntries(data)
    } catch (error) {
      console.error('加载日记失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateEntry = async () => {
    if (!newEntry.title.trim() || !newEntry.content.trim()) {
      alert('请填写标题和内容')
      return
    }

    try {
      const entry = await diaryAPI.createDiaryEntry({
        ...newEntry,
        user_id: user.id
      })
      
      setEntries([entry, ...entries])
      setNewEntry({ title: '', content: '', type: 'vanilla_diary', visibility: 'public' })
      setShowEditor(false)
      
      // 给予经验值奖励
      await fetch('/api/users/xp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.access_token}`
        },
        body: JSON.stringify({
          amount: 10,
          reason: '发布日记'
        })
      })
    } catch (error) {
      console.error('创建日记失败:', error)
      alert('发布失败，请重试')
    }
  }

  const handleLike = async (entryId) => {
    try {
      await diaryAPI.likeDiaryEntry(entryId, user.id)
      // 更新本地状态
      setEntries(entries.map(entry => 
        entry.id === entryId 
          ? { ...entry, likes: (entry.likes || 0) + 1 }
          : entry
      ))
    } catch (error) {
      console.error('点赞失败:', error)
    }
  }

  const DiaryEntry = ({ entry }) => (
    <div className="card-medieval">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-herb-green rounded-full flex items-center justify-center">
            <span className="text-parchment font-bold">
              {entry.users?.username?.charAt(0) || 'U'}
            </span>
          </div>
          <div>
            <h3 className="font-medium text-herb-green">{entry.users?.username}</h3>
            <div className="flex items-center space-x-2 text-sm text-sage-gray">
              <Calendar size={14} />
              <span>{new Date(entry.created_at).toLocaleDateString()}</span>
              <span className={`px-2 py-1 rounded text-xs ${
                entry.type === 'vanilla_diary' 
                  ? 'bg-herb-green/20 text-herb-green' 
                  : 'bg-rosemary-purple/20 text-rosemary-purple'
              }`}>
                {entry.type === 'vanilla_diary' ? '香草日记' : '浪人诗签'}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-1 text-sage-gray">
          <Eye size={14} />
          <span className="text-xs">{entry.visibility === 'public' ? '公开' : '私密'}</span>
        </div>
      </div>

      <h2 className="text-xl font-medieval text-herb-green mb-3">{entry.title}</h2>
      <div className="prose prose-sm max-w-none mb-4">
        <p className="text-herb-green whitespace-pre-wrap">{entry.content}</p>
      </div>

      {/* 特殊标记：晨雾行者诗篇 */}
      {entry.is_morning_mist && (
        <div className="bg-gradient-to-r from-rosemary-purple/20 to-herb-gold/20 p-4 rounded-lg mb-4 border-l-4 border-rosemary-purple">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-rosemary-purple">✨</span>
            <span className="text-sm font-medium text-rosemary-purple">晨雾行者的诗篇</span>
          </div>
          <p className="text-sm text-sage-gray italic">
            "在晨雾中行走的人，总能看到别人看不到的风景..."
          </p>
        </div>
      )}

      <div className="flex items-center justify-between pt-4 border-t border-sage-gray/30">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => handleLike(entry.id)}
            className="flex items-center space-x-2 text-sage-gray hover:text-herb-green transition-colors duration-200"
          >
            <Heart size={16} />
            <span className="text-sm">{entry.likes || 0}</span>
          </button>
          <button className="flex items-center space-x-2 text-sage-gray hover:text-herb-green transition-colors duration-200">
            <MessageCircle size={16} />
            <span className="text-sm">{entry.comments || 0}</span>
          </button>
        </div>
        <button className="flex items-center space-x-2 text-sage-gray hover:text-herb-green transition-colors duration-200">
          <Share size={16} />
          <span className="text-sm">分享</span>
        </button>
      </div>
    </div>
  )

  if (!user) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📖</div>
        <h2 className="text-2xl font-medieval text-herb-green mb-4">香草日记</h2>
        <p className="text-sage-gray mb-6">登录后开始记录你的集市生活</p>
        <button
          onClick={() => supabase.auth.signInWithOAuth({ provider: 'google' })}
          className="btn-medieval"
        >
          立即登录
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-medieval text-herb-green mb-2">香草日记</h1>
        <p className="text-sage-gray">记录生活点滴，分享美好时光</p>
      </div>

      {/* Create Entry Button */}
      <div className="text-center">
        <button
          onClick={() => setShowEditor(!showEditor)}
          className="btn-medieval inline-flex items-center space-x-2"
        >
          <PenTool size={18} />
          <span>写日记</span>
        </button>
      </div>

      {/* Editor */}
      {showEditor && (
        <div className="card-medieval">
          <h3 className="text-xl font-medieval text-herb-green mb-4">新日记</h3>
          
          <div className="space-y-4">
            <input
              type="text"
              placeholder="日记标题..."
              value={newEntry.title}
              onChange={(e) => setNewEntry({ ...newEntry, title: e.target.value })}
              className="w-full px-4 py-2 border border-sage-gray rounded-lg focus:outline-none focus:ring-2 focus:ring-herb-green"
            />

            <div className="grid grid-cols-2 gap-4">
              <select
                value={newEntry.type}
                onChange={(e) => setNewEntry({ ...newEntry, type: e.target.value })}
                className="px-4 py-2 border border-sage-gray rounded-lg focus:outline-none focus:ring-2 focus:ring-herb-green"
              >
                <option value="vanilla_diary">香草日记</option>
                <option value="wanderer_poem">浪人诗签</option>
              </select>

              <select
                value={newEntry.visibility}
                onChange={(e) => setNewEntry({ ...newEntry, visibility: e.target.value })}
                className="px-4 py-2 border border-sage-gray rounded-lg focus:outline-none focus:ring-2 focus:ring-herb-green"
              >
                <option value="public">公开</option>
                <option value="private">私密</option>
              </select>
            </div>

            <textarea
              placeholder="写下你的故事..."
              value={newEntry.content}
              onChange={(e) => setNewEntry({ ...newEntry, content: e.target.value })}
              rows={6}
              className="w-full px-4 py-2 border border-sage-gray rounded-lg focus:outline-none focus:ring-2 focus:ring-herb-green resize-none"
            />

            <div className="flex items-center justify-end space-x-4">
              <button
                onClick={() => setShowEditor(false)}
                className="px-6 py-2 text-sage-gray hover:text-herb-green transition-colors duration-200"
              >
                取消
              </button>
              <button
                onClick={handleCreateEntry}
                className="btn-medieval"
              >
                发布
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Diary Entries */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-herb-green mx-auto mb-4"></div>
          <p className="text-sage-gray">加载日记中...</p>
        </div>
      ) : entries.length > 0 ? (
        <div className="space-y-6">
          {entries.map((entry) => (
            <DiaryEntry key={entry.id} entry={entry} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📝</div>
          <h3 className="text-xl font-medieval text-herb-green mb-2">还没有日记</h3>
          <p className="text-sage-gray">开始记录你在斯卡布罗集市的故事吧</p>
        </div>
      )}
    </div>
  )
}

export default DiaryPage
