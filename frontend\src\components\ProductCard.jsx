import React, { useState } from 'react'
import { 
  ShoppingCart, 
  Heart, 
  Star, 
  MapPin, 
  Eye, 
  Coins,
  Package,
  Clock
} from 'lucide-react'

const ProductCard = ({ product, user, onPurchase, onViewDetails, compact = false }) => {
  const [isLiked, setIsLiked] = useState(false)
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)

  const handleLike = () => {
    setIsLiked(!isLiked)
    // TODO: 实现点赞功能
  }

  const handleQuickPurchase = () => {
    if (!user) {
      alert('请先登录')
      return
    }
    setShowPurchaseModal(true)
  }

  const PurchaseModal = () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-parchment rounded-lg max-w-md w-full p-6">
        <h3 className="text-xl font-medieval text-herb-green mb-4">购买商品</h3>
        
        <div className="space-y-4 mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-16 h-16 bg-sage-gray/20 rounded-lg flex items-center justify-center">
              <Package size={24} className="text-sage-gray" />
            </div>
            <div>
              <h4 className="font-medium text-herb-green">{product.name}</h4>
              <p className="text-sm text-sage-gray">{product.merchants?.name}</p>
              <p className="text-lg font-bold text-herb-gold">¥{product.price}</p>
            </div>
          </div>
          
          <div className="border-t border-sage-gray/30 pt-4">
            <h5 className="font-medium text-herb-green mb-2">支付方式</h5>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input type="radio" name="payment" value="online" defaultChecked />
                <span className="text-sm">在线支付</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="radio" name="payment" value="vanilla_coins" />
                <span className="text-sm flex items-center space-x-1">
                  <Coins size={14} className="text-herb-gold" />
                  <span>香草币支付</span>
                </span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="radio" name="payment" value="offline" />
                <span className="text-sm">线下支付</span>
              </label>
            </div>
          </div>
        </div>
        
        <div className="flex space-x-4">
          <button
            onClick={() => setShowPurchaseModal(false)}
            className="flex-1 px-4 py-2 border border-sage-gray rounded-lg hover:bg-sage-gray/20 transition-colors duration-200"
          >
            取消
          </button>
          <button
            onClick={() => {
              onPurchase && onPurchase(product)
              setShowPurchaseModal(false)
            }}
            className="flex-1 btn-medieval"
          >
            确认购买
          </button>
        </div>
      </div>
    </div>
  )

  if (compact) {
    return (
      <div className="flex items-center space-x-3 p-3 border border-sage-gray rounded-lg hover:shadow-md transition-shadow duration-300">
        <div className="w-12 h-12 bg-sage-gray/20 rounded-lg flex items-center justify-center flex-shrink-0">
          <Package size={20} className="text-sage-gray" />
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-herb-green truncate">{product.name}</h4>
          <p className="text-sm text-sage-gray truncate">{product.merchants?.name}</p>
        </div>
        <div className="text-right">
          <p className="font-bold text-herb-gold">¥{product.price}</p>
          <button
            onClick={() => onViewDetails && onViewDetails(product)}
            className="text-xs text-herb-green hover:underline"
          >
            查看
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="card-medieval hover:scale-105 transition-transform duration-300 group">
        {/* 商品图片 */}
        <div className="relative aspect-square bg-sage-gray/20 rounded-lg mb-4 overflow-hidden">
          {product.images && product.images.length > 0 ? (
            <img 
              src={product.images[0]} 
              alt={product.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Package size={48} className="text-sage-gray" />
            </div>
          )}
          
          {/* 悬浮操作按钮 */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button
              onClick={handleLike}
              className={`p-2 rounded-full shadow-lg transition-colors duration-200 ${
                isLiked 
                  ? 'bg-red-500 text-white' 
                  : 'bg-white/90 text-sage-gray hover:text-red-500'
              }`}
            >
              <Heart size={16} fill={isLiked ? 'currentColor' : 'none'} />
            </button>
          </div>
          
          {/* 库存状态 */}
          {product.stock_quantity <= 5 && product.stock_quantity > 0 && (
            <div className="absolute bottom-2 left-2">
              <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded">
                仅剩 {product.stock_quantity} 件
              </span>
            </div>
          )}
          
          {product.stock_quantity === 0 && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <span className="bg-red-500 text-white px-3 py-1 rounded">售罄</span>
            </div>
          )}
        </div>

        {/* 商品信息 */}
        <div className="space-y-3">
          <div>
            <h3 className="font-medium text-herb-green mb-1 line-clamp-2">{product.name}</h3>
            <p className="text-sm text-sage-gray line-clamp-2">{product.description}</p>
          </div>
          
          {/* 商户信息 */}
          <div className="flex items-center space-x-2">
            <MapPin size={14} className="text-sage-gray flex-shrink-0" />
            <span className="text-sm text-sage-gray truncate">{product.merchants?.location}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-sage-gray">by</span>
            <span className="text-sm font-medium text-herb-green truncate">{product.merchants?.name}</span>
            {product.merchants?.users?.level && (
              <div className="flex items-center space-x-1">
                <Star size={12} className="text-herb-gold fill-current" />
                <span className="text-xs text-sage-gray">Lv.{product.merchants.users.level}</span>
              </div>
            )}
          </div>
          
          {/* 价格和分类 */}
          <div className="flex items-center justify-between">
            <span className="text-xl font-bold text-herb-gold">¥{product.price}</span>
            <span className="text-xs bg-sage-gray/20 px-2 py-1 rounded">{product.category}</span>
          </div>
          
          {/* 可用条件 */}
          {product.availability_conditions && (
            <div className="flex items-center space-x-2 text-xs text-sage-gray">
              <Clock size={12} />
              <span>特定条件可用</span>
            </div>
          )}
          
          {/* 标签 */}
          {product.tags && product.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {product.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="text-xs bg-herb-green/20 text-herb-green px-2 py-1 rounded"
                >
                  {tag}
                </span>
              ))}
              {product.tags.length > 3 && (
                <span className="text-xs text-sage-gray">+{product.tags.length - 3}</span>
              )}
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2 mt-4">
          <button
            onClick={() => onViewDetails && onViewDetails(product)}
            className="flex-1 flex items-center justify-center space-x-2 py-2 border border-herb-green text-herb-green rounded-lg hover:bg-herb-green hover:text-parchment transition-colors duration-200"
          >
            <Eye size={16} />
            <span>详情</span>
          </button>
          
          <button
            onClick={handleQuickPurchase}
            disabled={product.stock_quantity === 0}
            className="flex-1 flex items-center justify-center space-x-2 py-2 bg-herb-green text-parchment rounded-lg hover:bg-rosemary-purple transition-colors duration-200 disabled:bg-sage-gray disabled:cursor-not-allowed"
          >
            <ShoppingCart size={16} />
            <span>{product.stock_quantity === 0 ? '售罄' : '购买'}</span>
          </button>
        </div>
      </div>
      
      {showPurchaseModal && <PurchaseModal />}
    </>
  )
}

export default ProductCard
