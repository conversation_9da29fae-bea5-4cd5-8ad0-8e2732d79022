import React, { useState } from 'react'
import { 
  Code, 
  Key, 
  Book, 
  Copy, 
  CheckCircle, 
  ExternalLink,
  Terminal,
  Zap,
  Shield,
  Globe
} from 'lucide-react'

const DeveloperAPIPage = ({ user }) => {
  const [activeTab, setActiveTab] = useState('overview')
  const [copiedCode, setCopiedCode] = useState('')

  const copyToClipboard = (text, id) => {
    navigator.clipboard.writeText(text)
    setCopiedCode(id)
    setTimeout(() => setCopiedCode(''), 2000)
  }

  const apiEndpoints = [
    {
      category: '用户管理',
      endpoints: [
        {
          method: 'GET',
          path: '/api/users/profile',
          description: '获取用户档案信息',
          auth: true,
          example: `curl -X GET "https://api.scarborough-fair.com/users/profile" \\
  -H "Authorization: Bearer YOUR_API_KEY"`
        },
        {
          method: 'PUT',
          path: '/api/users/profile',
          description: '更新用户档案',
          auth: true,
          example: `curl -X PUT "https://api.scarborough-fair.com/users/profile" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{"username": "new_username", "bio": "Updated bio"}'`
        }
      ]
    },
    {
      category: '商品管理',
      endpoints: [
        {
          method: 'GET',
          path: '/api/products',
          description: '获取商品列表',
          auth: false,
          example: `curl -X GET "https://api.scarborough-fair.com/products?category=草本香料&limit=10"`
        },
        {
          method: 'POST',
          path: '/api/merchants/{merchantId}/products',
          description: '创建新商品',
          auth: true,
          example: `curl -X POST "https://api.scarborough-fair.com/merchants/123/products" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "新鲜芜荽",
    "description": "来自斯卡布罗田野的新鲜芜荽",
    "price": 15.50,
    "category": "草本香料",
    "stock_quantity": 50
  }'`
        }
      ]
    },
    {
      category: '订单管理',
      endpoints: [
        {
          method: 'GET',
          path: '/api/orders',
          description: '获取订单列表',
          auth: true,
          example: `curl -X GET "https://api.scarborough-fair.com/orders?status=completed" \\
  -H "Authorization: Bearer YOUR_API_KEY"`
        },
        {
          method: 'POST',
          path: '/api/orders',
          description: '创建新订单',
          auth: true,
          example: `curl -X POST "https://api.scarborough-fair.com/orders" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "product_id": "product-uuid",
    "quantity": 2,
    "payment_type": "online"
  }'`
        }
      ]
    },
    {
      category: '游戏化系统',
      endpoints: [
        {
          method: 'GET',
          path: '/api/tasks',
          description: '获取用户任务',
          auth: true,
          example: `curl -X GET "https://api.scarborough-fair.com/tasks?status=active" \\
  -H "Authorization: Bearer YOUR_API_KEY"`
        },
        {
          method: 'POST',
          path: '/api/tasks/{taskId}/complete',
          description: '完成任务',
          auth: true,
          example: `curl -X POST "https://api.scarborough-fair.com/tasks/task-uuid/complete" \\
  -H "Authorization: Bearer YOUR_API_KEY"`
        }
      ]
    }
  ]

  const sdkExamples = {
    javascript: `// 安装 SDK
npm install @scarborough-fair/sdk

// 使用示例
import ScarboroughAPI from '@scarborough-fair/sdk'

const api = new ScarboroughAPI({
  apiKey: 'your-api-key',
  environment: 'production' // 或 'sandbox'
})

// 获取商品列表
const products = await api.products.list({
  category: '草本香料',
  limit: 10
})

// 创建订单
const order = await api.orders.create({
  productId: 'product-uuid',
  quantity: 2,
  paymentType: 'vanilla_coins'
})`,
    python: `# 安装 SDK
pip install scarborough-fair-sdk

# 使用示例
from scarborough_fair import ScarboroughAPI

api = ScarboroughAPI(
    api_key='your-api-key',
    environment='production'
)

# 获取商品列表
products = api.products.list(
    category='草本香料',
    limit=10
)

# 创建订单
order = api.orders.create(
    product_id='product-uuid',
    quantity=2,
    payment_type='vanilla_coins'
)`,
    php: `<?php
// 安装 SDK
composer require scarborough-fair/sdk

// 使用示例
use ScarboroughFair\\ScarboroughAPI;

$api = new ScarboroughAPI([
    'api_key' => 'your-api-key',
    'environment' => 'production'
]);

// 获取商品列表
$products = $api->products->list([
    'category' => '草本香料',
    'limit' => 10
]);

// 创建订单
$order = $api->orders->create([
    'product_id' => 'product-uuid',
    'quantity' => 2,
    'payment_type' => 'vanilla_coins'
]);`
  }

  const webhookEvents = [
    {
      event: 'order.created',
      description: '新订单创建时触发',
      payload: `{
  "event": "order.created",
  "data": {
    "id": "order-uuid",
    "user_id": "user-uuid",
    "product_id": "product-uuid",
    "quantity": 2,
    "total_price": 31.00,
    "status": "pending",
    "created_at": "2024-01-15T10:30:00Z"
  }
}`
    },
    {
      event: 'order.completed',
      description: '订单完成时触发',
      payload: `{
  "event": "order.completed",
  "data": {
    "id": "order-uuid",
    "payment_status": "completed",
    "completed_at": "2024-01-15T11:00:00Z"
  }
}`
    },
    {
      event: 'user.level_up',
      description: '用户升级时触发',
      payload: `{
  "event": "user.level_up",
  "data": {
    "user_id": "user-uuid",
    "old_level": 9,
    "new_level": 10,
    "xp": 1000,
    "rewards": {
      "coins": 500,
      "badges": ["level_10"]
    }
  }
}`
    }
  ]

  const tabs = [
    { id: 'overview', label: '概览', icon: Globe },
    { id: 'authentication', label: '认证', icon: Shield },
    { id: 'endpoints', label: 'API 端点', icon: Terminal },
    { id: 'sdk', label: 'SDK', icon: Code },
    { id: 'webhooks', label: 'Webhooks', icon: Zap }
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-medieval text-herb-green mb-2">开发者 API</h1>
        <p className="text-sage-gray">构建与斯卡布罗集市集成的应用程序</p>
      </div>

      {/* API Key Section */}
      {user && (
        <div className="card-medieval bg-gradient-to-r from-herb-green/10 to-rosemary-purple/10">
          <div className="flex items-center space-x-3 mb-4">
            <Key className="text-herb-green" size={24} />
            <h3 className="text-xl font-medieval text-herb-green">您的 API 密钥</h3>
          </div>
          <div className="bg-sage-gray/20 rounded-lg p-4 font-mono text-sm">
            <div className="flex items-center justify-between">
              <span>sk_live_1234567890abcdef...</span>
              <button
                onClick={() => copyToClipboard('sk_live_1234567890abcdef...', 'api-key')}
                className="flex items-center space-x-1 text-herb-green hover:text-rosemary-purple"
              >
                {copiedCode === 'api-key' ? <CheckCircle size={16} /> : <Copy size={16} />}
                <span className="text-xs">{copiedCode === 'api-key' ? '已复制' : '复制'}</span>
              </button>
            </div>
          </div>
          <p className="text-xs text-sage-gray mt-2">
            ⚠️ 请妥善保管您的 API 密钥，不要在客户端代码中暴露
          </p>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-sage-gray/30">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-herb-green text-herb-green'
                    : 'border-transparent text-sage-gray hover:text-herb-green'
                }`}
              >
                <Icon size={16} />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="card-medieval">
              <h3 className="text-xl font-medieval text-herb-green mb-4">API 概览</h3>
              <div className="prose prose-sm max-w-none text-sage-gray">
                <p>
                  斯卡布罗集市 API 允许您构建与我们平台集成的应用程序。
                  您可以管理商品、处理订单、访问用户数据，以及利用我们独特的游戏化功能。
                </p>
                <h4 className="text-herb-green">主要特性</h4>
                <ul>
                  <li>RESTful API 设计</li>
                  <li>JSON 格式数据交换</li>
                  <li>OAuth 2.0 认证</li>
                  <li>实时 Webhook 通知</li>
                  <li>多语言 SDK 支持</li>
                  <li>沙盒环境测试</li>
                </ul>
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="card-medieval text-center">
                <div className="text-3xl mb-3">🚀</div>
                <h4 className="font-medieval text-herb-green mb-2">快速开始</h4>
                <p className="text-sm text-sage-gray">
                  几分钟内完成 API 集成
                </p>
              </div>
              <div className="card-medieval text-center">
                <div className="text-3xl mb-3">📚</div>
                <h4 className="font-medieval text-herb-green mb-2">详细文档</h4>
                <p className="text-sm text-sage-gray">
                  完整的 API 参考和示例
                </p>
              </div>
              <div className="card-medieval text-center">
                <div className="text-3xl mb-3">🛠️</div>
                <h4 className="font-medieval text-herb-green mb-2">开发工具</h4>
                <p className="text-sm text-sage-gray">
                  SDK、测试工具和调试器
                </p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'authentication' && (
          <div className="card-medieval">
            <h3 className="text-xl font-medieval text-herb-green mb-4">API 认证</h3>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-herb-green mb-2">Bearer Token 认证</h4>
                <p className="text-sm text-sage-gray mb-3">
                  在请求头中包含您的 API 密钥：
                </p>
                <div className="bg-sage-gray/20 rounded-lg p-4 font-mono text-sm">
                  <div className="flex items-center justify-between">
                    <span>Authorization: Bearer YOUR_API_KEY</span>
                    <button
                      onClick={() => copyToClipboard('Authorization: Bearer YOUR_API_KEY', 'auth-header')}
                      className="text-herb-green hover:text-rosemary-purple"
                    >
                      {copiedCode === 'auth-header' ? <CheckCircle size={16} /> : <Copy size={16} />}
                    </button>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-herb-green mb-2">环境</h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="border border-sage-gray/30 rounded-lg p-4">
                    <h5 className="font-medium text-sage-gray mb-2">沙盒环境</h5>
                    <p className="text-sm text-sage-gray mb-2">用于开发和测试</p>
                    <code className="text-xs bg-sage-gray/20 px-2 py-1 rounded">
                      https://sandbox-api.scarborough-fair.com
                    </code>
                  </div>
                  <div className="border border-sage-gray/30 rounded-lg p-4">
                    <h5 className="font-medium text-sage-gray mb-2">生产环境</h5>
                    <p className="text-sm text-sage-gray mb-2">用于正式应用</p>
                    <code className="text-xs bg-sage-gray/20 px-2 py-1 rounded">
                      https://api.scarborough-fair.com
                    </code>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'endpoints' && (
          <div className="space-y-6">
            {apiEndpoints.map((category, categoryIndex) => (
              <div key={categoryIndex} className="card-medieval">
                <h3 className="text-xl font-medieval text-herb-green mb-4">
                  {category.category}
                </h3>
                <div className="space-y-4">
                  {category.endpoints.map((endpoint, endpointIndex) => (
                    <div key={endpointIndex} className="border border-sage-gray/30 rounded-lg p-4">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className={`px-2 py-1 text-xs font-mono rounded ${
                          endpoint.method === 'GET' ? 'bg-green-100 text-green-700' :
                          endpoint.method === 'POST' ? 'bg-blue-100 text-blue-700' :
                          endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }`}>
                          {endpoint.method}
                        </span>
                        <code className="font-mono text-sm">{endpoint.path}</code>
                        {endpoint.auth && (
                          <span className="text-xs bg-rosemary-purple/20 text-rosemary-purple px-2 py-1 rounded">
                            需要认证
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-sage-gray mb-3">{endpoint.description}</p>
                      <div className="bg-sage-gray/20 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-sage-gray">示例请求</span>
                          <button
                            onClick={() => copyToClipboard(endpoint.example, `endpoint-${categoryIndex}-${endpointIndex}`)}
                            className="text-herb-green hover:text-rosemary-purple"
                          >
                            {copiedCode === `endpoint-${categoryIndex}-${endpointIndex}` ? <CheckCircle size={16} /> : <Copy size={16} />}
                          </button>
                        </div>
                        <pre className="text-xs font-mono whitespace-pre-wrap text-sage-gray">
                          {endpoint.example}
                        </pre>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'sdk' && (
          <div className="space-y-6">
            <div className="card-medieval">
              <h3 className="text-xl font-medieval text-herb-green mb-4">官方 SDK</h3>
              <p className="text-sage-gray mb-6">
                我们提供多种编程语言的 SDK，让您更轻松地集成我们的 API。
              </p>
              
              <div className="space-y-6">
                {Object.entries(sdkExamples).map(([language, code]) => (
                  <div key={language} className="border border-sage-gray/30 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-herb-green capitalize">{language}</h4>
                      <button
                        onClick={() => copyToClipboard(code, `sdk-${language}`)}
                        className="flex items-center space-x-1 text-herb-green hover:text-rosemary-purple"
                      >
                        {copiedCode === `sdk-${language}` ? <CheckCircle size={16} /> : <Copy size={16} />}
                        <span className="text-xs">{copiedCode === `sdk-${language}` ? '已复制' : '复制'}</span>
                      </button>
                    </div>
                    <pre className="bg-sage-gray/20 rounded-lg p-4 text-xs font-mono overflow-x-auto text-sage-gray">
                      {code}
                    </pre>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'webhooks' && (
          <div className="space-y-6">
            <div className="card-medieval">
              <h3 className="text-xl font-medieval text-herb-green mb-4">Webhook 事件</h3>
              <p className="text-sage-gray mb-6">
                当特定事件发生时，我们会向您配置的端点发送 HTTP POST 请求。
              </p>
              
              <div className="space-y-4">
                {webhookEvents.map((webhook, index) => (
                  <div key={index} className="border border-sage-gray/30 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <code className="font-mono text-sm text-herb-green">{webhook.event}</code>
                      <button
                        onClick={() => copyToClipboard(webhook.payload, `webhook-${index}`)}
                        className="text-herb-green hover:text-rosemary-purple"
                      >
                        {copiedCode === `webhook-${index}` ? <CheckCircle size={16} /> : <Copy size={16} />}
                      </button>
                    </div>
                    <p className="text-sm text-sage-gray mb-3">{webhook.description}</p>
                    <div className="bg-sage-gray/20 rounded-lg p-3">
                      <span className="text-xs font-medium text-sage-gray">示例载荷</span>
                      <pre className="text-xs font-mono mt-2 whitespace-pre-wrap text-sage-gray">
                        {webhook.payload}
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer Links */}
      <div className="card-medieval bg-gradient-to-r from-herb-green/10 to-rosemary-purple/10">
        <h3 className="text-lg font-medieval text-herb-green mb-4">更多资源</h3>
        <div className="grid md:grid-cols-3 gap-4">
          <a href="#" className="flex items-center space-x-2 text-herb-green hover:text-rosemary-purple">
            <Book size={16} />
            <span>完整 API 文档</span>
            <ExternalLink size={14} />
          </a>
          <a href="#" className="flex items-center space-x-2 text-herb-green hover:text-rosemary-purple">
            <Terminal size={16} />
            <span>API 测试工具</span>
            <ExternalLink size={14} />
          </a>
          <a href="#" className="flex items-center space-x-2 text-herb-green hover:text-rosemary-purple">
            <Code size={16} />
            <span>GitHub 仓库</span>
            <ExternalLink size={14} />
          </a>
        </div>
      </div>
    </div>
  )
}

export default DeveloperAPIPage
