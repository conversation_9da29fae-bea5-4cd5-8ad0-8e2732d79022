import express from 'express'
import { authMiddleware } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { supabase } from '../services/supabase.js'
import Stripe from 'stripe'

const router = express.Router()
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

// 获取用户订单列表
router.get('/', authMiddleware, asyncHandler(async (req, res) => {
  const { status, limit = 20, offset = 0 } = req.query
  
  let query = supabase
    .from('orders')
    .select(`
      *,
      products (
        name,
        price,
        images
      ),
      merchants (
        name,
        location
      )
    `)
    .eq('user_id', req.user.id)
    .order('created_at', { ascending: false })
  
  if (status) {
    query = query.eq('order_status', status)
  }
  
  query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)
  
  const { data, error, count } = await query
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: count
    }
  })
}))

// 获取订单详情
router.get('/:id', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const { data: order, error } = await supabase
    .from('orders')
    .select(`
      *,
      products (
        name,
        description,
        price,
        images,
        category
      ),
      merchants (
        name,
        location,
        contact_info,
        users (
          username
        )
      )
    `)
    .eq('id', id)
    .single()
  
  if (error) {
    return res.status(404).json({
      success: false,
      error: '订单不存在'
    })
  }
  
  // 检查权限：只有订单所有者或商户可以查看
  const isMerchant = await supabase
    .from('merchants')
    .select('user_id')
    .eq('id', order.merchant_id)
    .single()
  
  if (order.user_id !== req.user.id && isMerchant.data?.user_id !== req.user.id) {
    return res.status(403).json({
      success: false,
      error: '无权查看此订单'
    })
  }
  
  res.json({
    success: true,
    data: order
  })
}))

// 创建订单
router.post('/', authMiddleware, asyncHandler(async (req, res) => {
  const { product_id, quantity = 1, payment_type = 'online', delivery_info, notes } = req.body
  
  // 获取商品信息
  const { data: product, error: productError } = await supabase
    .from('products')
    .select(`
      *,
      merchants (
        id,
        name,
        user_id
      )
    `)
    .eq('id', product_id)
    .eq('status', 'active')
    .single()
  
  if (productError || !product) {
    return res.status(404).json({
      success: false,
      error: '商品不存在或已下架'
    })
  }
  
  // 检查库存
  if (product.stock_quantity < quantity) {
    return res.status(400).json({
      success: false,
      error: '库存不足'
    })
  }
  
  const total_price = product.price * quantity
  
  // 如果使用香草币支付
  if (payment_type === 'vanilla_coins') {
    const result = await supabase.rpc('purchase_product', {
      buyer_id: req.user.id,
      product_id,
      quantity,
      payment_type: 'vanilla_coins'
    })
    
    if (result.error) throw result.error
    
    const purchaseResult = result.data[0]
    
    if (!purchaseResult.success) {
      return res.status(400).json({
        success: false,
        error: purchaseResult.message
      })
    }
    
    // 获取创建的订单详情
    const { data: order } = await supabase
      .from('orders')
      .select(`
        *,
        products (name, price),
        merchants (name)
      `)
      .eq('id', purchaseResult.order_id)
      .single()
    
    return res.status(201).json({
      success: true,
      data: order,
      message: '订单创建成功，已使用香草币支付'
    })
  }
  
  // 在线支付流程
  if (payment_type === 'online') {
    try {
      // 创建 Stripe 支付意图
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(total_price * 100), // Stripe 使用分为单位
        currency: 'cny',
        metadata: {
          product_id,
          user_id: req.user.id,
          merchant_id: product.merchants.id,
          quantity: quantity.toString()
        }
      })
      
      // 创建待支付订单
      const { data: order, error } = await supabase
        .from('orders')
        .insert({
          user_id: req.user.id,
          merchant_id: product.merchants.id,
          product_id,
          quantity,
          total_price,
          payment_type: 'online',
          payment_status: 'pending',
          order_status: 'pending',
          delivery_info,
          notes
        })
        .select(`
          *,
          products (name, price),
          merchants (name)
        `)
        .single()
      
      if (error) throw error
      
      res.status(201).json({
        success: true,
        data: {
          order,
          client_secret: paymentIntent.client_secret
        },
        message: '订单创建成功，请完成支付'
      })
    } catch (stripeError) {
      console.error('Stripe 错误:', stripeError)
      return res.status(500).json({
        success: false,
        error: '支付系统错误'
      })
    }
  }
  
  // 线下支付
  if (payment_type === 'offline') {
    const { data: order, error } = await supabase
      .from('orders')
      .insert({
        user_id: req.user.id,
        merchant_id: product.merchants.id,
        product_id,
        quantity,
        total_price,
        payment_type: 'offline',
        payment_status: 'pending',
        order_status: 'pending',
        delivery_info,
        notes
      })
      .select(`
        *,
        products (name, price),
        merchants (name, contact_info)
      `)
      .single()
    
    if (error) throw error
    
    // 更新库存
    await supabase
      .from('products')
      .update({ stock_quantity: product.stock_quantity - quantity })
      .eq('id', product_id)
    
    res.status(201).json({
      success: true,
      data: order,
      message: '订单创建成功，请联系商户完成线下支付'
    })
  }
}))

// 更新订单状态 (商户操作)
router.put('/:id/status', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  const { order_status, payment_status } = req.body
  
  // 获取订单信息
  const { data: order, error: orderError } = await supabase
    .from('orders')
    .select(`
      *,
      merchants (user_id)
    `)
    .eq('id', id)
    .single()
  
  if (orderError || !order) {
    return res.status(404).json({
      success: false,
      error: '订单不存在'
    })
  }
  
  // 检查权限：只有商户可以更新订单状态
  if (order.merchants.user_id !== req.user.id) {
    return res.status(403).json({
      success: false,
      error: '无权操作此订单'
    })
  }
  
  const updateData = {}
  if (order_status) updateData.order_status = order_status
  if (payment_status) updateData.payment_status = payment_status
  updateData.updated_at = new Date().toISOString()
  
  const { data: updatedOrder, error } = await supabase
    .from('orders')
    .update(updateData)
    .eq('id', id)
    .select(`
      *,
      products (name),
      users (username)
    `)
    .single()
  
  if (error) throw error
  
  // 发送状态更新通知给买家
  await supabase
    .from('messages')
    .insert({
      sender_id: req.user.id,
      recipient_id: order.user_id,
      title: '订单状态更新',
      content: `您的订单 ${order.id} 状态已更新为：${order_status || order.order_status}`,
      type: 'system'
    })
  
  res.json({
    success: true,
    data: updatedOrder,
    message: '订单状态更新成功'
  })
}))

// 取消订单
router.put('/:id/cancel', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  const { reason } = req.body
  
  const { data: order, error: orderError } = await supabase
    .from('orders')
    .select('*')
    .eq('id', id)
    .eq('user_id', req.user.id)
    .single()
  
  if (orderError || !order) {
    return res.status(404).json({
      success: false,
      error: '订单不存在'
    })
  }
  
  // 只能取消待处理的订单
  if (order.order_status !== 'pending') {
    return res.status(400).json({
      success: false,
      error: '只能取消待处理的订单'
    })
  }
  
  // 更新订单状态
  const { data: cancelledOrder, error } = await supabase
    .from('orders')
    .update({
      order_status: 'cancelled',
      notes: order.notes ? `${order.notes}\n取消原因：${reason}` : `取消原因：${reason}`,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()
  
  if (error) throw error
  
  // 恢复库存
  await supabase
    .from('products')
    .update({ 
      stock_quantity: supabase.raw('stock_quantity + ?', [order.quantity])
    })
    .eq('id', order.product_id)
  
  // 如果是香草币支付，退还香草币
  if (order.payment_type === 'vanilla_coins' && order.payment_status === 'completed') {
    await supabase.rpc('add_vanilla_coins', {
      user_id: req.user.id,
      coin_amount: order.total_price
    })
  }
  
  res.json({
    success: true,
    data: cancelledOrder,
    message: '订单取消成功'
  })
}))

// Stripe Webhook 处理支付结果
router.post('/webhook', express.raw({ type: 'application/json' }), asyncHandler(async (req, res) => {
  const sig = req.headers['stripe-signature']
  let event
  
  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET)
  } catch (err) {
    console.error('Webhook 签名验证失败:', err.message)
    return res.status(400).send(`Webhook Error: ${err.message}`)
  }
  
  // 处理支付成功事件
  if (event.type === 'payment_intent.succeeded') {
    const paymentIntent = event.data.object
    const { product_id, user_id, merchant_id, quantity } = paymentIntent.metadata
    
    // 更新订单状态
    await supabase
      .from('orders')
      .update({
        payment_status: 'completed',
        order_status: 'confirmed'
      })
      .eq('user_id', user_id)
      .eq('product_id', product_id)
      .eq('payment_status', 'pending')
    
    // 更新库存
    await supabase
      .from('products')
      .update({ 
        stock_quantity: supabase.raw('stock_quantity - ?', [parseInt(quantity)])
      })
      .eq('id', product_id)
    
    // 给用户增加经验值
    await supabase.rpc('add_user_xp', {
      user_id,
      xp_amount: 10 * parseInt(quantity)
    })
    
    // 发送支付成功通知
    await supabase
      .from('messages')
      .insert({
        sender_id: user_id,
        recipient_id: user_id,
        title: '支付成功',
        content: '您的订单支付已成功，商户将尽快处理您的订单。',
        type: 'system'
      })
  }
  
  res.json({ received: true })
}))

export default router
