import { supabaseAuth } from '../services/supabase.js'

// 认证中间件
export const authMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '缺少访问令牌'
      })
    }

    const token = authHeader.substring(7) // 移除 'Bearer ' 前缀

    // 验证 JWT 令牌
    const { data: { user }, error } = await supabaseAuth.auth.getUser(token)

    if (error || !user) {
      return res.status(401).json({
        success: false,
        error: '无效的访问令牌'
      })
    }

    // 将用户信息添加到请求对象
    req.user = user
    next()
  } catch (error) {
    console.error('认证中间件错误:', error)
    res.status(401).json({
      success: false,
      error: '认证失败'
    })
  }
}

// 可选认证中间件（用户可能已登录也可能未登录）
export const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const { data: { user }, error } = await supabaseAuth.auth.getUser(token)
      
      if (!error && user) {
        req.user = user
      }
    }
    
    next()
  } catch (error) {
    // 可选认证失败时不阻止请求继续
    console.warn('可选认证失败:', error)
    next()
  }
}

// 角色验证中间件
export const requireRole = (roles) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: '需要登录'
        })
      }

      // 从数据库获取用户角色信息
      const { data: userProfile, error } = await supabaseAuth
        .from('users')
        .select('role, organization')
        .eq('id', req.user.id)
        .single()

      if (error) {
        return res.status(500).json({
          success: false,
          error: '获取用户信息失败'
        })
      }

      const userRole = userProfile?.role || 'user'
      
      if (!roles.includes(userRole)) {
        return res.status(403).json({
          success: false,
          error: '权限不足'
        })
      }

      req.userProfile = userProfile
      next()
    } catch (error) {
      console.error('角色验证错误:', error)
      res.status(500).json({
        success: false,
        error: '角色验证失败'
      })
    }
  }
}

// 商户权限验证
export const requireMerchantAccess = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: '需要登录'
      })
    }

    const merchantId = req.params.merchantId || req.body.merchantId

    if (!merchantId) {
      return res.status(400).json({
        success: false,
        error: '缺少商户ID'
      })
    }

    // 检查用户是否拥有该商户
    const { data: merchant, error } = await supabaseAuth
      .from('merchants')
      .select('user_id')
      .eq('id', merchantId)
      .single()

    if (error) {
      return res.status(404).json({
        success: false,
        error: '商户不存在'
      })
    }

    if (merchant.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: '无权访问该商户'
      })
    }

    next()
  } catch (error) {
    console.error('商户权限验证错误:', error)
    res.status(500).json({
      success: false,
      error: '权限验证失败'
    })
  }
}
