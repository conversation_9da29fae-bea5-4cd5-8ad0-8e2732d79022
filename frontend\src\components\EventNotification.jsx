import React, { useState, useEffect } from 'react'
import { 
  X, 
  Calendar, 
  <PERSON>, 
  Star, 
  Coins, 
  Zap,
  CloudRain,
  Sun,
  Cloud,
  Snowflake
} from 'lucide-react'
import { formatTime } from '../utils/helpers'

const EventNotification = ({ events = [], onParticipate, onDismiss }) => {
  const [visibleEvents, setVisibleEvents] = useState([])
  const [dismissedEvents, setDismissedEvents] = useState(new Set())

  useEffect(() => {
    // 过滤掉已关闭的事件
    const activeEvents = events.filter(event => !dismissedEvents.has(event.id))
    setVisibleEvents(activeEvents)
  }, [events, dismissedEvents])

  const handleDismiss = (eventId) => {
    setDismissedEvents(prev => new Set([...prev, eventId]))
    onDismiss && onDismiss(eventId)
  }

  const getEventIcon = (type) => {
    switch (type) {
      case 'weather':
        return <CloudRain size={20} className="text-blue-500" />
      case 'time':
        return <Clock size={20} className="text-purple-500" />
      case 'special':
        return <Star size={20} className="text-yellow-500" />
      default:
        return <Zap size={20} className="text-herb-green" />
    }
  }

  const getWeatherIcon = (weather) => {
    switch (weather) {
      case 'sunny':
        return <Sun size={16} className="text-yellow-500" />
      case 'rainy':
        return <CloudRain size={16} className="text-blue-500" />
      case 'cloudy':
        return <Cloud size={16} className="text-gray-500" />
      case 'snowy':
        return <Snowflake size={16} className="text-blue-300" />
      default:
        return null
    }
  }

  const formatTimeRemaining = (endTime) => {
    const now = new Date()
    const end = new Date(endTime)
    const diff = end - now

    if (diff <= 0) return '已结束'

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `${hours}小时${minutes}分钟后结束`
    } else {
      return `${minutes}分钟后结束`
    }
  }

  if (visibleEvents.length === 0) return null

  return (
    <div className="fixed top-20 right-4 z-40 space-y-3 max-w-sm">
      {visibleEvents.map((event) => (
        <div
          key={event.id}
          className="bg-parchment border-2 border-herb-gold rounded-lg shadow-xl p-4 animate-slide-in-right"
        >
          {/* 事件头部 */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-2">
              {getEventIcon(event.type)}
              <div>
                <h3 className="font-medieval text-herb-green font-semibold">
                  {event.title}
                </h3>
                <div className="flex items-center space-x-2 text-xs text-sage-gray">
                  <Calendar size={12} />
                  <span>{formatTime(event.start_time)}</span>
                  {event.trigger_conditions?.weather && (
                    <>
                      {getWeatherIcon(event.trigger_conditions.weather)}
                      <span className="capitalize">{event.trigger_conditions.weather}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            <button
              onClick={() => handleDismiss(event.id)}
              className="text-sage-gray hover:text-herb-green transition-colors duration-200"
            >
              <X size={16} />
            </button>
          </div>

          {/* 事件描述 */}
          <p className="text-sm text-sage-gray mb-3 leading-relaxed">
            {event.description}
          </p>

          {/* 事件效果 */}
          {event.effects && (
            <div className="mb-3 p-2 bg-herb-green/10 rounded-lg">
              <h4 className="text-xs font-medium text-herb-green mb-1">事件效果</h4>
              <div className="text-xs text-sage-gray">
                {event.effects.discount && (
                  <div>• {event.effects.discount.category} 商品 -{event.effects.discount.percentage}% 折扣</div>
                )}
                {event.effects.xp_multiplier && (
                  <div>• {event.effects.xp_multiplier.action === 'diary_post' ? '发布日记' : '特定行为'} 经验值 x{event.effects.xp_multiplier.multiplier}</div>
                )}
                {event.effects.exploration_bonus && (
                  <div>• 探索奖励 x{event.effects.exploration_bonus.multiplier}</div>
                )}
              </div>
            </div>
          )}

          {/* 奖励信息 */}
          {event.rewards && (
            <div className="mb-3">
              <h4 className="text-xs font-medium text-herb-green mb-2">参与奖励</h4>
              <div className="flex flex-wrap gap-2">
                {event.rewards.xp && (
                  <div className="flex items-center space-x-1 text-xs bg-herb-green/20 text-herb-green px-2 py-1 rounded">
                    <Star size={12} />
                    <span>{event.rewards.xp} XP</span>
                  </div>
                )}
                {event.rewards.coins && (
                  <div className="flex items-center space-x-1 text-xs bg-herb-gold/20 text-herb-gold px-2 py-1 rounded">
                    <Coins size={12} />
                    <span>{event.rewards.coins} 香草币</span>
                  </div>
                )}
                {event.rewards.xp_bonus && (
                  <div className="flex items-center space-x-1 text-xs bg-rosemary-purple/20 text-rosemary-purple px-2 py-1 rounded">
                    <Zap size={12} />
                    <span>+{event.rewards.xp_bonus} XP 加成</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 时间信息 */}
          <div className="mb-3 text-xs text-sage-gray">
            <Clock size={12} className="inline mr-1" />
            {formatTimeRemaining(event.end_time)}
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-2">
            <button
              onClick={() => onParticipate && onParticipate(event)}
              className="flex-1 bg-herb-green hover:bg-rosemary-purple text-parchment text-sm py-2 px-3 rounded-lg transition-colors duration-200"
            >
              参与事件
            </button>
            <button
              onClick={() => handleDismiss(event.id)}
              className="px-3 py-2 border border-sage-gray text-sage-gray text-sm rounded-lg hover:bg-sage-gray/20 transition-colors duration-200"
            >
              稍后
            </button>
          </div>

          {/* 事件类型标签 */}
          <div className="absolute top-2 right-2">
            <span className={`text-xs px-2 py-1 rounded-full ${
              event.type === 'weather' ? 'bg-blue-100 text-blue-700' :
              event.type === 'time' ? 'bg-purple-100 text-purple-700' :
              event.type === 'special' ? 'bg-yellow-100 text-yellow-700' :
              'bg-green-100 text-green-700'
            }`}>
              {event.type === 'weather' ? '天气' :
               event.type === 'time' ? '时间' :
               event.type === 'special' ? '特殊' : '事件'}
            </span>
          </div>
        </div>
      ))}
    </div>
  )
}

// 事件管理 Hook
export const useEventNotifications = () => {
  const [events, setEvents] = useState([])
  const [loading, setLoading] = useState(false)

  const loadActiveEvents = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/events/active')
      const result = await response.json()
      
      if (result.success) {
        setEvents(result.data)
      }
    } catch (error) {
      console.error('加载活跃事件失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const participateInEvent = async (event, userToken) => {
    try {
      const response = await fetch(`/api/events/${event.id}/participate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      })
      
      const result = await response.json()
      
      if (result.success) {
        // 显示奖励通知
        return result
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('参与事件失败:', error)
      throw error
    }
  }

  useEffect(() => {
    loadActiveEvents()
    
    // 每5分钟检查一次新事件
    const interval = setInterval(loadActiveEvents, 5 * 60 * 1000)
    
    return () => clearInterval(interval)
  }, [])

  return {
    events,
    loading,
    loadActiveEvents,
    participateInEvent
  }
}

export default EventNotification
