import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('缺少 Supabase 配置环境变量')
}

// 创建 Supabase 客户端 (使用 service role key 用于服务端操作)
export const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// 创建用于用户认证的客户端 (使用 anon key)
export const supabaseAuth = createClient(supabaseUrl, process.env.SUPABASE_ANON_KEY)

// 数据库操作辅助函数
export const dbHelpers = {
  // 安全查询用户数据
  async getUserById(userId) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error) throw error
    return data
  },

  // 创建新用户档案
  async createUserProfile(userData) {
    const { data, error } = await supabase
      .from('users')
      .insert(userData)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // 更新用户经验值和等级
  async updateUserXP(userId, xpAmount) {
    const { data, error } = await supabase.rpc('add_user_xp', {
      user_id: userId,
      xp_amount: xpAmount
    })
    
    if (error) throw error
    return data
  },

  // 获取商户信息
  async getMerchantById(merchantId) {
    const { data, error } = await supabase
      .from('merchants')
      .select(`
        *,
        users (
          id,
          username,
          level
        )
      `)
      .eq('id', merchantId)
      .single()
    
    if (error) throw error
    return data
  },

  // 获取商品列表
  async getProducts(filters = {}) {
    let query = supabase
      .from('products')
      .select(`
        *,
        merchants (
          id,
          name,
          location,
          users (
            username
          )
        )
      `)
      .eq('status', 'active')
    
    if (filters.category) {
      query = query.eq('category', filters.category)
    }
    
    if (filters.location) {
      query = query.eq('merchants.location', filters.location)
    }
    
    if (filters.priceRange) {
      query = query.gte('price', filters.priceRange.min)
                   .lte('price', filters.priceRange.max)
    }
    
    const { data, error } = await query.order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // 创建订单
  async createOrder(orderData) {
    const { data, error } = await supabase
      .from('orders')
      .insert(orderData)
      .select(`
        *,
        products (
          name,
          price,
          merchants (
            name
          )
        ),
        users (
          username
        )
      `)
      .single()
    
    if (error) throw error
    return data
  },

  // 获取用户任务
  async getUserTasks(userId, status = 'active') {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('user_id', userId)
      .eq('status', status)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // 完成任务并给予奖励
  async completeTask(taskId, userId) {
    const { data, error } = await supabase.rpc('complete_task', {
      task_id: taskId,
      user_id: userId
    })
    
    if (error) throw error
    return data
  },

  // 创建日记条目
  async createDiaryEntry(entryData) {
    const { data, error } = await supabase
      .from('diary_entries')
      .insert(entryData)
      .select(`
        *,
        users (
          username,
          level
        )
      `)
      .single()
    
    if (error) throw error
    return data
  },

  // 获取公开日记
  async getPublicDiaryEntries(limit = 20) {
    const { data, error } = await supabase
      .from('diary_entries')
      .select(`
        *,
        users (
          username,
          level
        )
      `)
      .eq('visibility', 'public')
      .order('created_at', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data
  }
}
