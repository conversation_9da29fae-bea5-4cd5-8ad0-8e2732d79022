import express from 'express'
import { authMiddleware, optionalAuth } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { dbHelpers, supabase } from '../services/supabase.js'

const router = express.Router()

// 获取用户档案
router.get(
	'/profile',
	authMiddleware,
	asyncHandler(async (req, res) => {
		const user = await dbHelpers.getUserById(req.user.id)

		res.json({
			success: true,
			data: {
				id: user.id,
				username: user.username,
				email: user.email,
				level: user.level,
				xp: user.xp,
				vanilla_coins: user.vanilla_coins,
				organization: user.organization,
				badges: user.badges,
				avatar_url: user.avatar_url,
				created_at: user.created_at,
				last_login: user.last_login
			}
		})
	})
)

// 更新用户档案
router.put(
	'/profile',
	authMiddleware,
	asyncHandler(async (req, res) => {
		const { username, avatar_url, organization } = req.body

		const { data, error } = await supabase
			.from('users')
			.update({
				username,
				avatar_url,
				organization,
				updated_at: new Date().toISOString()
			})
			.eq('id', req.user.id)
			.select()
			.single()

		if (error) throw error

		res.json({
			success: true,
			data,
			message: '档案更新成功'
		})
	})
)

// 获取用户统计信息
router.get(
	'/stats',
	authMiddleware,
	asyncHandler(async (req, res) => {
		const userId = req.user.id

		// 并行查询各种统计数据
		const [
			{ count: totalOrders },
			{ count: totalProducts },
			{ count: completedTasks },
			{ count: diaryEntries }
		] = await Promise.all([
			supabase
				.from('orders')
				.select('*', { count: 'exact', head: true })
				.eq('user_id', userId),
			supabase
				.from('products')
				.select('*', { count: 'exact', head: true })
				.eq('merchant_id', userId),
			supabase
				.from('tasks')
				.select('*', { count: 'exact', head: true })
				.eq('user_id', userId)
				.eq('status', 'completed'),
			supabase
				.from('diary_entries')
				.select('*', { count: 'exact', head: true })
				.eq('user_id', userId)
		])

		res.json({
			success: true,
			data: {
				totalOrders,
				totalProducts,
				completedTasks,
				diaryEntries
			}
		})
	})
)

// 增加经验值
router.post(
	'/xp',
	authMiddleware,
	asyncHandler(async (req, res) => {
		const { amount, reason } = req.body

		if (!amount || amount <= 0) {
			return res.status(400).json({
				success: false,
				error: '经验值数量必须大于0'
			})
		}

		const result = await dbHelpers.updateUserXP(req.user.id, amount)

		res.json({
			success: true,
			data: result,
			message: `获得 ${amount} 经验值！${reason ? `原因：${reason}` : ''}`
		})
	})
)

// 增加香草币
router.post(
	'/coins',
	authMiddleware,
	asyncHandler(async (req, res) => {
		const { amount, reason } = req.body

		if (!amount || amount <= 0) {
			return res.status(400).json({
				success: false,
				error: '香草币数量必须大于0'
			})
		}

		const { data, error } = await supabase.rpc('add_vanilla_coins', {
			user_id: req.user.id,
			coin_amount: amount
		})

		if (error) throw error

		res.json({
			success: true,
			data,
			message: `获得 ${amount} 香草币！${reason ? `原因：${reason}` : ''}`
		})
	})
)

// 获取用户徽章
router.get(
	'/badges',
	authMiddleware,
	asyncHandler(async (req, res) => {
		const { data: user } = await supabase
			.from('users')
			.select('badges')
			.eq('id', req.user.id)
			.single()

		res.json({
			success: true,
			data: user.badges || []
		})
	})
)

// 解锁徽章
router.post(
	'/badges',
	authMiddleware,
	asyncHandler(async (req, res) => {
		const { badgeId, badgeName } = req.body

		const { data: user } = await supabase
			.from('users')
			.select('badges')
			.eq('id', req.user.id)
			.single()

		const currentBadges = user.badges || []

		// 检查徽章是否已存在
		if (currentBadges.some((badge) => badge.id === badgeId)) {
			return res.status(400).json({
				success: false,
				error: '徽章已解锁'
			})
		}

		const newBadge = {
			id: badgeId,
			name: badgeName,
			unlocked_at: new Date().toISOString()
		}

		const { data, error } = await supabase
			.from('users')
			.update({
				badges: [...currentBadges, newBadge]
			})
			.eq('id', req.user.id)
			.select()
			.single()

		if (error) throw error

		res.json({
			success: true,
			data: newBadge,
			message: `恭喜解锁徽章：${badgeName}！`
		})
	})
)

// 获取排行榜
router.get(
	'/leaderboard',
	optionalAuth,
	asyncHandler(async (req, res) => {
		const { type = 'level', limit = 10 } = req.query

		let orderBy = 'level'
		if (type === 'xp') orderBy = 'xp'
		if (type === 'coins') orderBy = 'vanilla_coins'

		const { data, error } = await supabase
			.from('users')
			.select('id, username, level, xp, vanilla_coins, avatar_url')
			.order(orderBy, { ascending: false })
			.limit(parseInt(limit))

		if (error) throw error

		res.json({
			success: true,
			data
		})
	})
)

export default router
