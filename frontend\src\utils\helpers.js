// 工具函数集合

// 格式化时间
export const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  // 小于1分钟
  if (diff < 60000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000)
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000)
    return `${hours}小时前`
  }
  
  // 小于7天
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000)
    return `${days}天前`
  }
  
  // 超过7天显示具体日期
  return date.toLocaleDateString('zh-CN')
}

// 格式化价格
export const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2
  }).format(price)
}

// 格式化数字（添加千分位分隔符）
export const formatNumber = (num) => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

// 计算等级进度
export const calculateLevelProgress = (xp) => {
  const currentLevel = Math.floor(xp / 100) + 1
  const currentLevelXP = (currentLevel - 1) * 100
  const nextLevelXP = currentLevel * 100
  const progress = ((xp - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100
  
  return {
    currentLevel,
    progress: Math.min(progress, 100),
    nextLevelXP,
    currentLevelXP,
    xpToNext: nextLevelXP - xp
  }
}

// 获取等级称号
export const getLevelTitle = (level) => {
  if (level >= 50) return '传奇商人'
  if (level >= 40) return '大师级商人'
  if (level >= 30) return '专家级商人'
  if (level >= 20) return '熟练商人'
  if (level >= 10) return '见习商人'
  if (level >= 5) return '新手商人'
  return '流浪者'
}

// 获取天气图标
export const getWeatherIcon = (weather) => {
  const icons = {
    sunny: '☀️',
    cloudy: '☁️',
    rainy: '🌧️',
    snowy: '❄️',
    foggy: '🌫️'
  }
  return icons[weather] || '🌤️'
}

// 获取时间段
export const getTimeOfDay = () => {
  const hour = new Date().getHours()
  if (hour >= 5 && hour < 12) return 'morning'
  if (hour >= 12 && hour < 17) return 'afternoon'
  if (hour >= 17 && hour < 21) return 'evening'
  return 'night'
}

// 获取时间段问候语
export const getGreeting = () => {
  const timeOfDay = getTimeOfDay()
  const greetings = {
    morning: '早上好',
    afternoon: '下午好',
    evening: '晚上好',
    night: '夜深了'
  }
  return greetings[timeOfDay]
}

// 生成随机ID
export const generateId = () => {
  return Math.random().toString(36).substr(2, 9)
}

// 防抖函数
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
export const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 深拷贝
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

// 检查是否为移动设备
export const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 复制到剪贴板
export const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      document.execCommand('copy')
      document.body.removeChild(textArea)
      return true
    } catch (err) {
      document.body.removeChild(textArea)
      return false
    }
  }
}

// 获取文件大小的可读格式
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 验证邮箱格式
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证手机号格式
export const isValidPhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// 生成颜色
export const generateColor = (str) => {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }
  const hue = hash % 360
  return `hsl(${hue}, 70%, 60%)`
}

// 获取对比色
export const getContrastColor = (hexColor) => {
  // 移除 # 号
  const color = hexColor.replace('#', '')
  
  // 转换为 RGB
  const r = parseInt(color.substr(0, 2), 16)
  const g = parseInt(color.substr(2, 2), 16)
  const b = parseInt(color.substr(4, 2), 16)
  
  // 计算亮度
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  
  // 返回黑色或白色
  return brightness > 128 ? '#000000' : '#FFFFFF'
}

// 本地存储工具
export const storage = {
  set: (key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('存储失败:', error)
    }
  },
  
  get: (key, defaultValue = null) => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error('读取存储失败:', error)
      return defaultValue
    }
  },
  
  remove: (key) => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('删除存储失败:', error)
    }
  },
  
  clear: () => {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('清空存储失败:', error)
    }
  }
}

// 草本植物相关的工具函数
export const herbUtils = {
  // 获取草本植物的功效描述
  getHerbEffects: (herbName) => {
    const effects = {
      '芜荽': '温中健胃，消食化积',
      '鼠尾草': '清热解毒，消炎止痛',
      '迷迭香': '提神醒脑，增强记忆',
      '百里香': '止咳化痰，杀菌消炎'
    }
    return effects[herbName] || '天然草本，功效独特'
  },
  
  // 获取草本植物的颜色主题
  getHerbColor: (herbName) => {
    const colors = {
      '芜荽': '#4A7043', // 草绿色
      '鼠尾草': '#B0B7A4', // 鼠尾灰
      '迷迭香': '#6B4E71', // 迷迭香紫
      '百里香': '#D4A017'  // 草本金黄
    }
    return colors[herbName] || '#4A7043'
  },
  
  // 获取草本植物的季节性
  getHerbSeason: (herbName) => {
    const seasons = {
      '芜荽': ['春', '夏'],
      '鼠尾草': ['夏', '秋'],
      '迷迭香': ['春', '夏', '秋'],
      '百里香': ['夏', '秋']
    }
    return seasons[herbName] || ['春', '夏', '秋', '冬']
  }
}
