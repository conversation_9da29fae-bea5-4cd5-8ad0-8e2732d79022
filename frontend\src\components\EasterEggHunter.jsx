import React, { useState, useEffect } from 'react'
import { 
  Search, 
  Eye, 
  Clock, 
  Calendar, 
  Star, 
  Gift,
  Sparkles,
  Moon,
  Sun,
  Cloud
} from 'lucide-react'

const EasterEggHunter = ({ user, currentLocation, currentTime }) => {
  const [discoveredEggs, setDiscoveredEggs] = useState([])
  const [activeHints, setActiveHints] = useState([])
  const [showHunter, setShowHunter] = useState(false)

  // 彩蛋定义
  const easterEggs = [
    {
      id: 'dawn_whisper',
      name: '黎明低语',
      description: '在黎明时分访问草本花园，聆听芙萝拉的秘密',
      type: 'time_location',
      conditions: {
        time: 'dawn', // 5-7点
        location: 'herb_garden',
        user_level: 5
      },
      rewards: { xp: 200, coins: 300, special_item: 'dawn_dew' },
      rarity: 'rare',
      hint: '当第一缕阳光洒向花园时，古老的秘密会被揭示...'
    },
    {
      id: 'midnight_merchant',
      name: '午夜商人',
      description: '在深夜探索独孤城，遇见神秘的午夜商人',
      type: 'time_location',
      conditions: {
        time: 'midnight', // 0-2点
        location: 'solitude',
        user_level: 15
      },
      rewards: { xp: 500, coins: 1000, special_item: 'shadow_cloak' },
      rarity: 'legendary',
      hint: '当月亮高悬，古城中会出现一个不属于这个时代的身影...'
    },
    {
      id: 'herb_symphony',
      name: '草本交响曲',
      description: '同时拥有四种经典草本时触发的特殊事件',
      type: 'collection',
      conditions: {
        items: ['芜荽', '鼠尾草', '迷迭香', '百里香'],
        all_required: true
      },
      rewards: { xp: 300, coins: 500, badge: 'herb_master' },
      rarity: 'epic',
      hint: '当四种草本齐聚，古老的歌谣将重新响起...'
    },
    {
      id: 'poets_secret',
      name: '诗人的秘密',
      description: '与莱瑞克对话100次后解锁的隐藏故事',
      type: 'npc_friendship',
      conditions: {
        npc: 'lyrick',
        friendship_level: 100
      },
      rewards: { xp: 1000, coins: 2000, story: 'lyrick_origin' },
      rarity: 'legendary',
      hint: '游吟诗人的心中藏着一个古老的秘密，只有真正的朋友才能知晓...'
    },
    {
      id: 'full_moon_hunt',
      name: '满月狩猎',
      description: '在满月之夜与凯恩一起狩猎',
      type: 'time_npc',
      conditions: {
        time: 'full_moon',
        npc: 'kane',
        location: 'hunters_lodge'
      },
      rewards: { xp: 400, coins: 600, special_item: 'moonlight_bow' },
      rarity: 'epic',
      hint: '当满月照亮森林，最好的猎手会邀请你加入他的狩猎...'
    },
    {
      id: 'weather_dance',
      name: '天气之舞',
      description: '在雨天、晴天、阴天各完成一次交易',
      type: 'weather_sequence',
      conditions: {
        weather_trades: ['rainy', 'sunny', 'cloudy'],
        sequence_required: false
      },
      rewards: { xp: 250, coins: 400, badge: 'weather_dancer' },
      rarity: 'rare',
      hint: '与天气共舞的商人，将获得自然的祝福...'
    },
    {
      id: 'level_palindrome',
      name: '回文等级',
      description: '达到回文数等级时的特殊奖励',
      type: 'level_special',
      conditions: {
        levels: [11, 22, 33, 44, 55]
      },
      rewards: { xp: 500, coins: 777, special_item: 'palindrome_charm' },
      rarity: 'rare',
      hint: '当数字成为镜像，命运也会反射出特殊的光芒...'
    },
    {
      id: 'coin_fountain',
      name: '香草币喷泉',
      description: '拥有特定数量香草币时的隐藏奖励',
      type: 'wealth_milestone',
      conditions: {
        coin_amounts: [1234, 5678, 9999]
      },
      rewards: { xp: 300, coins: 1000, special_item: 'golden_herb' },
      rarity: 'epic',
      hint: '当财富达到特殊的数字，金色的草本会从天而降...'
    }
  ]

  useEffect(() => {
    if (user) {
      checkEasterEggs()
      updateActiveHints()
    }
  }, [user, currentLocation, currentTime])

  // 检查彩蛋触发条件
  const checkEasterEggs = async () => {
    for (const egg of easterEggs) {
      if (discoveredEggs.includes(egg.id)) continue

      const canTrigger = await checkEggConditions(egg)
      if (canTrigger) {
        await triggerEasterEgg(egg)
      }
    }
  }

  // 检查彩蛋条件
  const checkEggConditions = async (egg) => {
    const conditions = egg.conditions

    switch (egg.type) {
      case 'time_location':
        return checkTimeLocation(conditions)
      case 'collection':
        return await checkCollection(conditions)
      case 'npc_friendship':
        return await checkNPCFriendship(conditions)
      case 'time_npc':
        return checkTimeNPC(conditions)
      case 'weather_sequence':
        return await checkWeatherSequence(conditions)
      case 'level_special':
        return checkLevelSpecial(conditions)
      case 'wealth_milestone':
        return checkWealthMilestone(conditions)
      default:
        return false
    }
  }

  // 检查时间和地点条件
  const checkTimeLocation = (conditions) => {
    const hour = new Date().getHours()
    let timeMatch = false

    switch (conditions.time) {
      case 'dawn':
        timeMatch = hour >= 5 && hour <= 7
        break
      case 'midnight':
        timeMatch = hour >= 0 && hour <= 2
        break
      case 'full_moon':
        // 简化的满月检查
        const day = new Date().getDate()
        timeMatch = day >= 13 && day <= 16
        break
    }

    const locationMatch = currentLocation === conditions.location
    const levelMatch = !conditions.user_level || user.level >= conditions.user_level

    return timeMatch && locationMatch && levelMatch
  }

  // 检查收集条件
  const checkCollection = async (conditions) => {
    // 这里应该检查用户的物品收集情况
    // 简化实现
    return Math.random() < 0.1 // 10% 概率模拟
  }

  // 检查NPC友谊度
  const checkNPCFriendship = async (conditions) => {
    try {
      const response = await fetch('/api/story/npc/friendships', {
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })
      const result = await response.json()
      
      if (result.success) {
        const npcFriendship = result.data.find(f => f.npc_id === conditions.npc)
        return npcFriendship?.friendship_level >= conditions.friendship_level
      }
    } catch (error) {
      console.error('检查NPC友谊度失败:', error)
    }
    return false
  }

  // 触发彩蛋
  const triggerEasterEgg = async (egg) => {
    try {
      // 记录发现的彩蛋
      setDiscoveredEggs(prev => [...prev, egg.id])

      // 显示彩蛋通知
      showEasterEggNotification(egg)

      // 给予奖励
      if (egg.rewards) {
        await giveEasterEggRewards(egg.rewards)
      }

      // 记录到后端
      await fetch('/api/story/easter-egg', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.access_token}`
        },
        body: JSON.stringify({
          egg_id: egg.id,
          discovered_at: new Date().toISOString()
        })
      })
    } catch (error) {
      console.error('触发彩蛋失败:', error)
    }
  }

  // 显示彩蛋通知
  const showEasterEggNotification = (egg) => {
    // 创建特殊的彩蛋发现动画
    const notification = document.createElement('div')
    notification.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 bg-gradient-to-r from-herb-gold to-rosemary-purple text-white p-6 rounded-lg shadow-2xl animate-bounce'
    notification.innerHTML = `
      <div class="text-center">
        <div class="text-4xl mb-2">🥚✨</div>
        <h3 class="text-xl font-medieval mb-2">彩蛋发现！</h3>
        <p class="text-lg">${egg.name}</p>
        <p class="text-sm opacity-90">${egg.description}</p>
      </div>
    `
    
    document.body.appendChild(notification)
    
    setTimeout(() => {
      notification.remove()
    }, 5000)
  }

  // 给予彩蛋奖励
  const giveEasterEggRewards = async (rewards) => {
    // 这里应该调用后端API给予奖励
    console.log('彩蛋奖励:', rewards)
  }

  // 更新活跃提示
  const updateActiveHints = () => {
    const hints = []
    const hour = new Date().getHours()

    // 时间相关提示
    if (hour >= 5 && hour <= 7) {
      hints.push({
        type: 'time',
        icon: <Sun className="text-herb-gold" size={16} />,
        text: '黎明时分，某些秘密可能会显现...'
      })
    }

    if (hour >= 0 && hour <= 2) {
      hints.push({
        type: 'time',
        icon: <Moon className="text-rosemary-purple" size={16} />,
        text: '深夜的探索可能带来意想不到的发现...'
      })
    }

    // 地点相关提示
    if (currentLocation === 'herb_garden') {
      hints.push({
        type: 'location',
        icon: <Sparkles className="text-herb-green" size={16} />,
        text: '花园中似乎隐藏着什么秘密...'
      })
    }

    setActiveHints(hints)
  }

  const getRarityColor = (rarity) => {
    switch (rarity) {
      case 'common':
        return 'text-gray-600'
      case 'rare':
        return 'text-blue-600'
      case 'epic':
        return 'text-purple-600'
      case 'legendary':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  const getRarityLabel = (rarity) => {
    switch (rarity) {
      case 'common':
        return '普通'
      case 'rare':
        return '稀有'
      case 'epic':
        return '史诗'
      case 'legendary':
        return '传奇'
      default:
        return '未知'
    }
  }

  if (!user) return null

  return (
    <>
      {/* 彩蛋猎人按钮 */}
      <button
        onClick={() => setShowHunter(!showHunter)}
        className="fixed bottom-4 right-4 w-12 h-12 bg-gradient-to-r from-herb-gold to-rosemary-purple text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40"
      >
        <Search size={20} />
      </button>

      {/* 活跃提示 */}
      {activeHints.length > 0 && (
        <div className="fixed top-20 right-4 space-y-2 z-30">
          {activeHints.map((hint, index) => (
            <div
              key={index}
              className="bg-herb-green/90 text-white p-3 rounded-lg shadow-lg max-w-xs animate-slide-in-right"
            >
              <div className="flex items-center space-x-2">
                {hint.icon}
                <span className="text-sm">{hint.text}</span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 彩蛋猎人面板 */}
      {showHunter && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-parchment rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">🥚</div>
                  <div>
                    <h2 className="text-2xl font-medieval text-herb-green">彩蛋猎人</h2>
                    <p className="text-sage-gray">发现隐藏在集市中的秘密</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowHunter(false)}
                  className="text-sage-gray hover:text-herb-green"
                >
                  ✕
                </button>
              </div>

              {/* 统计信息 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-3 bg-herb-green/10 rounded-lg">
                  <div className="text-2xl font-bold text-herb-green">{discoveredEggs.length}</div>
                  <div className="text-sm text-sage-gray">已发现</div>
                </div>
                <div className="text-center p-3 bg-sage-gray/10 rounded-lg">
                  <div className="text-2xl font-bold text-sage-gray">{easterEggs.length - discoveredEggs.length}</div>
                  <div className="text-sm text-sage-gray">未发现</div>
                </div>
                <div className="text-center p-3 bg-rosemary-purple/10 rounded-lg">
                  <div className="text-2xl font-bold text-rosemary-purple">
                    {Math.round((discoveredEggs.length / easterEggs.length) * 100)}%
                  </div>
                  <div className="text-sm text-sage-gray">完成度</div>
                </div>
                <div className="text-center p-3 bg-herb-gold/10 rounded-lg">
                  <div className="text-2xl font-bold text-herb-gold">{activeHints.length}</div>
                  <div className="text-sm text-sage-gray">活跃提示</div>
                </div>
              </div>

              {/* 彩蛋列表 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medieval text-herb-green">彩蛋收集</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  {easterEggs.map((egg) => {
                    const isDiscovered = discoveredEggs.includes(egg.id)
                    return (
                      <div
                        key={egg.id}
                        className={`p-4 rounded-lg border ${
                          isDiscovered
                            ? 'bg-green-50 border-green-200'
                            : 'bg-sage-gray/10 border-sage-gray/30'
                        }`}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <div className="text-2xl">
                              {isDiscovered ? '🥚' : '❓'}
                            </div>
                            <div>
                              <h4 className="font-medium text-herb-green">
                                {isDiscovered ? egg.name : '???'}
                              </h4>
                              <span className={`text-xs px-2 py-1 rounded ${getRarityColor(egg.rarity)}`}>
                                {getRarityLabel(egg.rarity)}
                              </span>
                            </div>
                          </div>
                          {isDiscovered && <CheckCircle className="text-green-500" size={20} />}
                        </div>
                        
                        <p className="text-sm text-sage-gray mb-3">
                          {isDiscovered ? egg.description : egg.hint}
                        </p>
                        
                        {isDiscovered && egg.rewards && (
                          <div className="flex flex-wrap gap-2">
                            {egg.rewards.xp && (
                              <span className="text-xs bg-herb-green/20 text-herb-green px-2 py-1 rounded">
                                +{egg.rewards.xp} XP
                              </span>
                            )}
                            {egg.rewards.coins && (
                              <span className="text-xs bg-herb-gold/20 text-herb-gold px-2 py-1 rounded">
                                +{egg.rewards.coins} 币
                              </span>
                            )}
                            {egg.rewards.special_item && (
                              <span className="text-xs bg-rosemary-purple/20 text-rosemary-purple px-2 py-1 rounded">
                                {egg.rewards.special_item}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default EasterEggHunter
