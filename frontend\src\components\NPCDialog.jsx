import React, { useState, useEffect } from 'react'
import { X, MessageCircle } from 'lucide-react'

const NPCDialog = ({ npc, isOpen, onClose, user }) => {
  const [currentDialog, setCurrentDialog] = useState(0)
  const [showRewards, setShowRewards] = useState(false)

  // NPC 对话内容
  const dialogues = {
    lyrick: {
      name: '莱瑞克',
      role: '游吟诗人',
      avatar: '🎭',
      dialogs: [
        {
          text: "欢迎来到斯卡布罗集市，朋友！我是莱瑞克，这里的游吟诗人。",
          options: ['你好，莱瑞克', '这里有什么特别的吗？']
        },
        {
          text: "这个集市充满了魔力，每一种草本都有它的故事。你听说过那首古老的歌谣吗？",
          options: ['什么歌谣？', '告诉我更多关于草本的事']
        },
        {
          text: "芜荽、鼠尾草、迷迭香、百里香...每一种都承载着古老的记忆。也许你能在这里找到属于自己的故事。",
          options: ['谢谢你的指引', '我想了解更多']
        }
      ],
      rewards: {
        xp: 20,
        coins: 10,
        item: null
      }
    },
    flora: {
      name: '芙萝拉',
      role: '草本商人',
      avatar: '🌿',
      dialogs: [
        {
          text: "你好，亲爱的！我是芙萝拉，专门经营各种草本植物。今天的香草特别新鲜呢～",
          options: ['我想看看你的商品', '你最推荐什么？']
        },
        {
          text: "迷迭香是我的最爱，它不仅香气怡人，还有提神醒脑的功效。要不要试试我新制的迷迭香精油？",
          options: ['好的，我很感兴趣', '还有其他推荐吗？']
        },
        {
          text: "当然！百里香蜂蜜也很棒，是蜜蜂采集百里香花蜜制成的。甜而不腻，还有淡淡的花香。",
          options: ['听起来很棒', '谢谢你的推荐']
        }
      ],
      rewards: {
        xp: 15,
        coins: 20,
        item: '草本种子'
      }
    },
    kane: {
      name: '凯恩',
      role: '伶仃猎手',
      avatar: '🏹',
      dialogs: [
        {
          text: "...你好。我是凯恩。如果你要进入森林，最好准备充足。",
          options: ['我需要什么装备？', '森林里有什么危险？']
        },
        {
          text: "野兽、陷阱、还有...其他东西。带上我的猎弓，至少能保护你的安全。",
          options: ['我想看看你的装备', '其他东西？']
        },
        {
          text: "有些事情...不适合在这里说。如果你真的想知道，完成一些狩猎任务再来找我。",
          options: ['我明白了', '我会证明自己的']
        }
      ],
      rewards: {
        xp: 25,
        coins: 15,
        item: '狩猎指南'
      }
    }
  }

  const currentNPCData = dialogues[npc?.id] || dialogues.lyrick

  useEffect(() => {
    if (isOpen) {
      setCurrentDialog(0)
      setShowRewards(false)
    }
  }, [isOpen])

  const handleOptionClick = (optionIndex) => {
    if (currentDialog < currentNPCData.dialogs.length - 1) {
      setCurrentDialog(currentDialog + 1)
    } else {
      // 对话结束，显示奖励
      setShowRewards(true)
      
      // 如果用户已登录，给予奖励
      if (user) {
        giveRewards()
      }
    }
  }

  const giveRewards = async () => {
    try {
      const rewards = currentNPCData.rewards
      
      if (rewards.xp > 0) {
        await fetch('/api/users/xp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${user.access_token}`
          },
          body: JSON.stringify({
            amount: rewards.xp,
            reason: `与${currentNPCData.name}对话`
          })
        })
      }
      
      if (rewards.coins > 0) {
        await fetch('/api/users/coins', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${user.access_token}`
          },
          body: JSON.stringify({
            amount: rewards.coins,
            reason: `与${currentNPCData.name}对话`
          })
        })
      }
    } catch (error) {
      console.error('给予奖励失败:', error)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-parchment rounded-lg max-w-md w-full shadow-2xl">
        {/* NPC 头部 */}
        <div className="bg-herb-green text-parchment p-4 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">{currentNPCData.avatar}</div>
              <div>
                <h3 className="font-medieval text-lg">{currentNPCData.name}</h3>
                <p className="text-sm opacity-90">{currentNPCData.role}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-1 hover:bg-rosemary-purple rounded transition-colors duration-200"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* 对话内容 */}
        <div className="p-6">
          {!showRewards ? (
            <>
              {/* 对话气泡 */}
              <div className="npc-dialog mb-6">
                <div className="flex items-start space-x-3">
                  <MessageCircle size={20} className="text-herb-green mt-1 flex-shrink-0" />
                  <p className="text-herb-green leading-relaxed">
                    {currentNPCData.dialogs[currentDialog]?.text}
                  </p>
                </div>
              </div>

              {/* 选项按钮 */}
              <div className="space-y-3">
                {currentNPCData.dialogs[currentDialog]?.options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleOptionClick(index)}
                    className="w-full text-left p-3 border border-sage-gray rounded-lg hover:bg-herb-green/10 hover:border-herb-green transition-colors duration-200"
                  >
                    <span className="text-herb-green">→ {option}</span>
                  </button>
                ))}
              </div>

              {/* 进度指示器 */}
              <div className="flex justify-center mt-6 space-x-2">
                {currentNPCData.dialogs.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full ${
                      index <= currentDialog ? 'bg-herb-green' : 'bg-sage-gray/30'
                    }`}
                  />
                ))}
              </div>
            </>
          ) : (
            /* 奖励显示 */
            <div className="text-center">
              <div className="text-4xl mb-4">🎁</div>
              <h4 className="text-xl font-medieval text-herb-green mb-4">
                对话完成！
              </h4>
              
              {user ? (
                <div className="space-y-3 mb-6">
                  <p className="text-sage-gray">你获得了以下奖励：</p>
                  
                  {currentNPCData.rewards.xp > 0 && (
                    <div className="flex items-center justify-center space-x-2 text-herb-green">
                      <span>⭐</span>
                      <span>{currentNPCData.rewards.xp} 经验值</span>
                    </div>
                  )}
                  
                  {currentNPCData.rewards.coins > 0 && (
                    <div className="flex items-center justify-center space-x-2 text-herb-gold">
                      <span>🪙</span>
                      <span>{currentNPCData.rewards.coins} 香草币</span>
                    </div>
                  )}
                  
                  {currentNPCData.rewards.item && (
                    <div className="flex items-center justify-center space-x-2 text-rosemary-purple">
                      <span>📦</span>
                      <span>{currentNPCData.rewards.item}</span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="mb-6">
                  <p className="text-sage-gray mb-4">
                    登录后可获得对话奖励！
                  </p>
                  <button
                    onClick={() => supabase.auth.signInWithOAuth({ provider: 'google' })}
                    className="btn-medieval"
                  >
                    立即登录
                  </button>
                </div>
              )}
              
              <button
                onClick={onClose}
                className="btn-medieval"
              >
                继续探索
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default NPCDialog
