import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import {
	ShoppingBag,
	BookOpen,
	Map,
	Star,
	Coins,
	TrendingUp,
	Users,
	MessageCircle
} from 'lucide-react'
import { productAPI, userAPI, supabase } from '../services/supabase'

const HomePage = ({ user }) => {
	const [recommendations, setRecommendations] = useState([])
	const [userStats, setUserStats] = useState(null)
	const [loading, setLoading] = useState(true)

	useEffect(() => {
		const loadData = async () => {
			try {
				// 获取推荐商品
				const products = await productAPI.getRecommendations({
					weather: 'sunny', // TODO: 从天气API获取实际天气
					limit: 6
				})
				setRecommendations(products)

				// 如果用户已登录，获取用户统计
				if (user) {
					const stats = await userAPI.getUserStats(user.id)
					setUserStats(stats)
				}
			} catch (error) {
				console.error('加载数据失败:', error)
			} finally {
				setLoading(false)
			}
		}

		loadData()
	}, [user])

	const features = [
		{
			icon: ShoppingBag,
			title: '集市交易',
			description: '发现独特商品，与商户直接交易',
			link: '/market',
			color: 'bg-herb-green'
		},
		{
			icon: BookOpen,
			title: '香草日记',
			description: '记录生活点滴，分享美好时光',
			link: '/diary',
			color: 'bg-rosemary-purple'
		},
		{
			icon: Map,
			title: '探索地图',
			description: '发现隐藏地点，解锁神秘奖励',
			link: '/map',
			color: 'bg-herb-gold'
		}
	]

	const npcs = [
		{
			name: '莱瑞克',
			role: '游吟诗人',
			image: '/assets/lyrick.png',
			message: '欢迎来到斯卡布罗集市，朋友！',
			animation: 'animate-bounce'
		},
		{
			name: '芙萝拉',
			role: '草本商人',
			image: '/assets/flora.png',
			message: '今天的香草特别新鲜呢～',
			animation: 'animate-pulse-slow'
		},
		{
			name: '凯恩',
			role: '伶仃猎手',
			image: '/assets/kane.png',
			message: '小心森林里的野兽...',
			animation: 'animate-float'
		}
	]

	if (loading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-herb-green mx-auto mb-4"></div>
					<p className="text-herb-green font-medieval">加载中...</p>
				</div>
			</div>
		)
	}

	return (
		<div className="space-y-12">
			{/* Hero Section */}
			<section className="text-center py-12 bg-gradient-to-br from-herb-green/10 to-rosemary-purple/10 rounded-2xl">
				<h1 className="text-4xl md:text-6xl font-medieval text-herb-green mb-4">
					斯卡布罗集市
				</h1>
				<p className="text-xl md:text-2xl text-sage-gray mb-6 italic">
					"Are you going to Scarborough Fair?"
				</p>
				<p className="text-lg text-herb-green mb-8 max-w-2xl mx-auto">
					在时间、天气与城市之间，遇见真正的人间市集。
					芜荽、鼠尾草、迷迭香、百里香，每一种草本都有它的故事。
				</p>

				{!user && (
					<button
						onClick={() =>
							supabase.auth.signInWithOAuth({ provider: 'google' })
						}
						className="btn-medieval text-lg px-8 py-4"
					>
						开始你的集市之旅
					</button>
				)}
			</section>

			{/* User Welcome Section */}
			{user && (
				<section className="card-medieval">
					<div className="flex items-center justify-between mb-6">
						<div>
							<h2 className="text-2xl font-medieval text-herb-green mb-2">
								欢迎回来，{user.username || '流浪者'}
							</h2>
							<p className="text-sage-gray">继续你在斯卡布罗集市的冒险吧！</p>
						</div>
						<div className="flex items-center space-x-4">
							<div className="text-center">
								<div className="flex items-center justify-center space-x-1 text-herb-gold">
									<Star size={20} />
									<span className="font-bold">Lv.{user.level || 1}</span>
								</div>
								<p className="text-xs text-sage-gray">等级</p>
							</div>
							<div className="text-center">
								<div className="flex items-center justify-center space-x-1 text-herb-gold">
									<Coins size={20} />
									<span className="font-bold">{user.vanilla_coins || 0}</span>
								</div>
								<p className="text-xs text-sage-gray">香草币</p>
							</div>
						</div>
					</div>

					{userStats && (
						<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
							<div className="text-center p-4 bg-herb-green/10 rounded-lg">
								<div className="text-2xl font-bold text-herb-green">
									{userStats.totalOrders}
								</div>
								<div className="text-sm text-sage-gray">订单数</div>
							</div>
							<div className="text-center p-4 bg-rosemary-purple/10 rounded-lg">
								<div className="text-2xl font-bold text-rosemary-purple">
									{userStats.completedTasks}
								</div>
								<div className="text-sm text-sage-gray">完成任务</div>
							</div>
							<div className="text-center p-4 bg-herb-gold/10 rounded-lg">
								<div className="text-2xl font-bold text-herb-gold">
									{userStats.diaryEntries}
								</div>
								<div className="text-sm text-sage-gray">日记条目</div>
							</div>
							<div className="text-center p-4 bg-sage-gray/10 rounded-lg">
								<div className="text-2xl font-bold text-sage-gray">
									{userStats.totalProducts}
								</div>
								<div className="text-sm text-sage-gray">发布商品</div>
							</div>
						</div>
					)}
				</section>
			)}

			{/* NPC Section */}
			<section className="card-medieval">
				<h2 className="text-2xl font-medieval text-herb-green mb-6 text-center">
					集市居民
				</h2>
				<div className="grid md:grid-cols-3 gap-6">
					{npcs.map((npc, index) => (
						<div key={index} className="text-center">
							<div className="relative mb-4">
								<div
									className={`w-24 h-24 mx-auto bg-sage-gray/20 rounded-full flex items-center justify-center ${npc.animation}`}
								>
									<span className="text-3xl">🎭</span>
								</div>
							</div>
							<h3 className="font-medieval text-lg text-herb-green mb-1">
								{npc.name}
							</h3>
							<p className="text-sm text-sage-gray mb-2">{npc.role}</p>
							<div className="npc-dialog">
								<p className="text-sm text-herb-green italic">
									"{npc.message}"
								</p>
							</div>
						</div>
					))}
				</div>
			</section>

			{/* Features Grid */}
			<section>
				<h2 className="text-3xl font-medieval text-herb-green text-center mb-8">
					探索集市功能
				</h2>
				<div className="grid md:grid-cols-3 gap-6">
					{features.map((feature, index) => (
						<Link
							key={index}
							to={feature.link}
							className="card-medieval hover:scale-105 transition-transform duration-300 group"
						>
							<div
								className={`w-16 h-16 ${feature.color} rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}
							>
								<feature.icon size={32} className="text-parchment" />
							</div>
							<h3 className="text-xl font-medieval text-herb-green mb-2">
								{feature.title}
							</h3>
							<p className="text-sage-gray">{feature.description}</p>
						</Link>
					))}
				</div>
			</section>

			{/* Recommendations */}
			{recommendations.length > 0 && (
				<section className="card-medieval">
					<div className="flex items-center justify-between mb-6">
						<h2 className="text-2xl font-medieval text-herb-green">今日推荐</h2>
						<Link
							to="/market"
							className="text-herb-gold hover:text-herb-green transition-colors duration-200"
						>
							查看更多 →
						</Link>
					</div>
					<div className="grid md:grid-cols-3 gap-6">
						{recommendations.slice(0, 3).map((product) => (
							<div
								key={product.id}
								className="border border-sage-gray rounded-lg p-4 hover:shadow-lg transition-shadow duration-300"
							>
								<div className="aspect-square bg-sage-gray/20 rounded-lg mb-3 flex items-center justify-center">
									<span className="text-4xl">📦</span>
								</div>
								<h3 className="font-medium text-herb-green mb-1">
									{product.name}
								</h3>
								<p className="text-sm text-sage-gray mb-2">
									{product.merchants?.name}
								</p>
								<div className="flex items-center justify-between">
									<span className="text-lg font-bold text-herb-gold">
										¥{product.price}
									</span>
									<span className="text-xs text-sage-gray">
										{product.category}
									</span>
								</div>
							</div>
						))}
					</div>
				</section>
			)}

			{/* Call to Action */}
			<section className="text-center py-12 bg-gradient-to-r from-herb-green to-rosemary-purple rounded-2xl text-parchment">
				<h2 className="text-3xl font-medieval mb-4">加入斯卡布罗集市</h2>
				<p className="text-lg mb-6 opacity-90">成为商户，开启你的创业之旅</p>
				<Link
					to="/merchant/register"
					className="bg-herb-gold hover:bg-herb-gold/80 text-herb-green px-8 py-3 rounded-lg font-medium transition-colors duration-200 inline-block"
				>
					申请开店
				</Link>
			</section>
		</div>
	)
}

export default HomePage
