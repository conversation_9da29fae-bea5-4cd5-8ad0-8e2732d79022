import { supabase } from './supabase.js'
import cron from 'node-cron'

// 动态事件系统
class EventSystem {
  constructor() {
    this.activeEvents = new Map()
    this.eventTemplates = new Map()
    this.initializeEventTemplates()
    this.startEventScheduler()
  }

  // 初始化事件模板
  initializeEventTemplates() {
    // 天气事件模板
    this.eventTemplates.set('sunny_herb_discount', {
      title: '晴日集市',
      description: '阳光明媚的日子里，所有草本植物的价格下降10%',
      type: 'weather',
      trigger_conditions: { weather: 'sunny' },
      effects: { 
        discount: { category: '草本香料', percentage: 10 }
      },
      rewards: { xp_bonus: 20 },
      duration_hours: 24,
      probability: 0.8 // 晴天时80%概率触发
    })

    this.eventTemplates.set('rainy_diary_bonus', {
      title: '雨夜诗会',
      description: '雨夜时分，发布日记可获得额外经验值',
      type: 'weather',
      trigger_conditions: { weather: 'rainy', time: 'night' },
      effects: { 
        xp_multiplier: { action: 'diary_post', multiplier: 2 }
      },
      rewards: { coins: 50 },
      duration_hours: 12,
      probability: 0.6
    })

    this.eventTemplates.set('cloudy_exploration', {
      title: '云雾缭绕',
      description: '多云的天气让探索变得更加神秘',
      type: 'weather',
      trigger_conditions: { weather: 'cloudy' },
      effects: { 
        exploration_bonus: { multiplier: 1.5 }
      },
      rewards: { xp: 30 },
      duration_hours: 18,
      probability: 0.7
    })

    // 时间事件模板
    this.eventTemplates.set('full_moon_hunt', {
      title: '月圆狩猎节',
      description: '满月之夜，狩猎装备销量翻倍，凯恩的小屋特别繁忙',
      type: 'time',
      trigger_conditions: { moon_phase: 'full' },
      effects: { 
        sales_boost: { merchant: '凯恩的狩猎小屋', multiplier: 2 }
      },
      rewards: { xp: 100, coins: 200 },
      duration_hours: 72,
      probability: 1.0 // 满月时必定触发
    })

    this.eventTemplates.set('dawn_market', {
      title: '晨曦集市',
      description: '清晨时分，早起的商户会给予额外折扣',
      type: 'time',
      trigger_conditions: { time: 'dawn' },
      effects: { 
        early_bird_discount: { percentage: 15 }
      },
      rewards: { xp: 25 },
      duration_hours: 3,
      probability: 0.3
    })

    // 特殊事件模板
    this.eventTemplates.set('herb_festival', {
      title: '草本节庆',
      description: '一年一度的草本节庆，所有草本相关活动都有特殊奖励',
      type: 'special',
      trigger_conditions: { date: 'herb_festival' },
      effects: { 
        herb_bonus: { xp_multiplier: 3, coin_multiplier: 2 }
      },
      rewards: { 
        xp: 500, 
        coins: 1000,
        badges: [{ id: 'herb_festival_2024', name: '草本节庆参与者' }]
      },
      duration_hours: 168, // 一周
      probability: 1.0
    })

    this.eventTemplates.set('merchant_gathering', {
      title: '商户聚会',
      description: '商户们聚集在一起分享经验，新商户注册可获得额外奖励',
      type: 'special',
      trigger_conditions: { trigger: 'manual' },
      effects: { 
        new_merchant_bonus: { xp: 200, coins: 500 }
      },
      rewards: { xp: 150 },
      duration_hours: 48,
      probability: 1.0
    })
  }

  // 启动事件调度器
  startEventScheduler() {
    // 每小时检查天气事件
    cron.schedule('0 * * * *', () => {
      this.checkWeatherEvents()
    })

    // 每天检查时间事件
    cron.schedule('0 0 * * *', () => {
      this.checkTimeEvents()
    })

    // 每分钟检查事件过期
    cron.schedule('* * * * *', () => {
      this.checkExpiredEvents()
    })

    console.log('🎪 事件调度器已启动')
  }

  // 检查天气事件
  async checkWeatherEvents() {
    try {
      // 这里应该从天气API获取实际天气
      // 暂时使用模拟数据
      const weather = this.simulateWeather()
      const timeOfDay = this.getTimeOfDay()

      for (const [eventId, template] of this.eventTemplates) {
        if (template.type !== 'weather') continue

        const conditions = template.trigger_conditions
        let shouldTrigger = false

        // 检查天气条件
        if (conditions.weather === weather) {
          if (!conditions.time || conditions.time === timeOfDay) {
            shouldTrigger = Math.random() < template.probability
          }
        }

        if (shouldTrigger) {
          await this.triggerEvent(eventId, template)
        }
      }
    } catch (error) {
      console.error('检查天气事件失败:', error)
    }
  }

  // 检查时间事件
  async checkTimeEvents() {
    try {
      const moonPhase = this.getMoonPhase()
      const timeOfDay = this.getTimeOfDay()
      const currentDate = new Date()

      for (const [eventId, template] of this.eventTemplates) {
        if (template.type !== 'time') continue

        const conditions = template.trigger_conditions
        let shouldTrigger = false

        // 检查月相
        if (conditions.moon_phase === moonPhase) {
          shouldTrigger = Math.random() < template.probability
        }

        // 检查时间
        if (conditions.time === timeOfDay) {
          shouldTrigger = Math.random() < template.probability
        }

        if (shouldTrigger) {
          await this.triggerEvent(eventId, template)
        }
      }
    } catch (error) {
      console.error('检查时间事件失败:', error)
    }
  }

  // 触发事件
  async triggerEvent(eventId, template) {
    try {
      // 检查是否已有相同类型的活跃事件
      const { data: existingEvents } = await supabase
        .from('events')
        .select('id')
        .eq('type', template.type)
        .eq('status', 'active')
        .gte('end_time', new Date().toISOString())

      if (existingEvents && existingEvents.length > 0) {
        console.log(`已有活跃的 ${template.type} 事件，跳过触发`)
        return
      }

      const startTime = new Date()
      const endTime = new Date(startTime.getTime() + template.duration_hours * 60 * 60 * 1000)

      const { data: event, error } = await supabase
        .from('events')
        .insert({
          title: template.title,
          description: template.description,
          type: template.type,
          trigger_conditions: template.trigger_conditions,
          effects: template.effects,
          rewards: template.rewards,
          start_time: startTime.toISOString(),
          end_time: endTime.toISOString(),
          status: 'active'
        })
        .select()
        .single()

      if (error) throw error

      this.activeEvents.set(event.id, event)
      console.log(`🎉 事件已触发: ${template.title}`)

      // 通知所有在线用户
      await this.notifyUsers(event)

    } catch (error) {
      console.error('触发事件失败:', error)
    }
  }

  // 手动触发特殊事件
  async triggerSpecialEvent(eventId, customData = {}) {
    try {
      const template = this.eventTemplates.get(eventId)
      if (!template) {
        throw new Error('事件模板不存在')
      }

      const mergedTemplate = { ...template, ...customData }
      await this.triggerEvent(eventId, mergedTemplate)
      
      return { success: true, message: `特殊事件 ${template.title} 已触发` }
    } catch (error) {
      console.error('触发特殊事件失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 检查过期事件
  async checkExpiredEvents() {
    try {
      const now = new Date().toISOString()

      const { data: expiredEvents, error } = await supabase
        .from('events')
        .update({ status: 'expired' })
        .lt('end_time', now)
        .eq('status', 'active')
        .select()

      if (error) throw error

      if (expiredEvents && expiredEvents.length > 0) {
        expiredEvents.forEach(event => {
          this.activeEvents.delete(event.id)
          console.log(`⏰ 事件已过期: ${event.title}`)
        })
      }
    } catch (error) {
      console.error('检查过期事件失败:', error)
    }
  }

  // 通知用户
  async notifyUsers(event) {
    try {
      // 这里可以实现实时通知系统
      // 例如使用 WebSocket 或 Server-Sent Events
      console.log(`📢 通知用户新事件: ${event.title}`)
    } catch (error) {
      console.error('通知用户失败:', error)
    }
  }

  // 模拟天气（实际应用中应该从天气API获取）
  simulateWeather() {
    const weathers = ['sunny', 'cloudy', 'rainy', 'snowy']
    const weights = [0.4, 0.3, 0.2, 0.1] // 权重
    
    const random = Math.random()
    let cumulative = 0
    
    for (let i = 0; i < weathers.length; i++) {
      cumulative += weights[i]
      if (random < cumulative) {
        return weathers[i]
      }
    }
    
    return 'sunny'
  }

  // 获取时间段
  getTimeOfDay() {
    const hour = new Date().getHours()
    if (hour >= 5 && hour < 12) return 'morning'
    if (hour >= 12 && hour < 17) return 'afternoon'
    if (hour >= 17 && hour < 21) return 'evening'
    if (hour >= 21 || hour < 5) return 'night'
    if (hour >= 4 && hour < 6) return 'dawn'
    return 'day'
  }

  // 获取月相（简化版）
  getMoonPhase() {
    const date = new Date()
    const day = date.getDate()
    
    // 简化的月相计算
    if (day >= 13 && day <= 16) return 'full'
    if (day >= 27 || day <= 2) return 'new'
    if (day >= 6 && day <= 9) return 'first_quarter'
    if (day >= 20 && day <= 23) return 'last_quarter'
    return 'waxing'
  }

  // 获取活跃事件
  async getActiveEvents() {
    try {
      const now = new Date().toISOString()
      
      const { data: events, error } = await supabase
        .from('events')
        .select('*')
        .eq('status', 'active')
        .lte('start_time', now)
        .gte('end_time', now)
        .order('start_time', { ascending: false })

      if (error) throw error

      return events || []
    } catch (error) {
      console.error('获取活跃事件失败:', error)
      return []
    }
  }

  // 应用事件效果
  applyEventEffects(action, data) {
    // 这里实现事件效果的应用逻辑
    // 例如：折扣、经验值加成等
    return data
  }
}

// 创建全局事件系统实例
export const eventSystem = new EventSystem()

export default EventSystem
