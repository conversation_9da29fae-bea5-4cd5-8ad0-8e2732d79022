import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import dotenv from 'dotenv'

// 导入路由
import userRoutes from './routes/users.js'
import merchantRoutes from './routes/merchants.js'
import productRoutes from './routes/products.js'
import orderRoutes from './routes/orders.js'
import taskRoutes from './routes/tasks.js'
import diaryRoutes from './routes/diary.js'
import mapRoutes from './routes/map.js'
import eventRoutes from './routes/events.js'

// 导入中间件
import { errorHandler } from './middleware/errorHandler.js'
import { authMiddleware } from './middleware/auth.js'

// 加载环境变量
dotenv.config()

const app = express()
const PORT = process.env.PORT || 3001

// 安全中间件
app.use(helmet())
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}))

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 100, // 限制每个 IP 15 分钟内最多 100 个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  }
})
app.use('/api/', limiter)

// 基础中间件
app.use(compression())
app.use(morgan('combined'))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: '斯卡布罗集市 API',
    version: '0.1.0'
  })
})

// API 路由
app.use('/api/users', userRoutes)
app.use('/api/merchants', merchantRoutes)
app.use('/api/products', productRoutes)
app.use('/api/orders', orderRoutes)
app.use('/api/tasks', taskRoutes)
app.use('/api/diary', diaryRoutes)
app.use('/api/map', mapRoutes)
app.use('/api/events', eventRoutes)

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: '路径不存在',
    message: `无法找到 ${req.method} ${req.originalUrl}`
  })
})

// 错误处理中间件
app.use(errorHandler)

// 启动服务器
app.listen(PORT, () => {
  console.log(`🏰 斯卡布罗集市服务器运行在端口 ${PORT}`)
  console.log(`📍 API 地址: http://localhost:${PORT}/api`)
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`)
})

export default app
