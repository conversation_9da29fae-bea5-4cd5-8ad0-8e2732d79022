import React, { useState, useEffect } from 'react'
import { Award, Lock, Star, Calendar, Trophy, Target } from 'lucide-react'
import { formatTime } from '../utils/helpers'

const BadgeCollection = ({ user, badges = [], showAll = false }) => {
  const [selectedBadge, setSelectedBadge] = useState(null)
  const [filter, setFilter] = useState('all')

  // 预定义的徽章系统
  const badgeDefinitions = {
    first_purchase: {
      id: 'first_purchase',
      name: '初次购买',
      description: '完成你在斯卡布罗集市的第一次购买',
      icon: '🛒',
      rarity: 'common',
      category: 'achievement'
    },
    herb_collector: {
      id: 'herb_collector',
      name: '草本收集者',
      description: '收集三种不同的草本植物',
      icon: '🌿',
      rarity: 'common',
      category: 'collection'
    },
    diary_writer: {
      id: 'diary_writer',
      name: '日记作家',
      description: '发布5篇日记',
      icon: '📖',
      rarity: 'common',
      category: 'social'
    },
    explorer: {
      id: 'explorer',
      name: '探索者',
      description: '解锁3个新地点',
      icon: '🗺️',
      rarity: 'uncommon',
      category: 'exploration'
    },
    morning_mist: {
      id: 'morning_mist',
      name: '晨雾行者',
      description: '创作了一篇晨雾行者诗篇',
      icon: '🌫️',
      rarity: 'rare',
      category: 'special'
    },
    merchant_master: {
      id: 'merchant_master',
      name: '商户大师',
      description: '成功经营商户并达到100笔交易',
      icon: '🏪',
      rarity: 'epic',
      category: 'business'
    },
    legendary_trader: {
      id: 'legendary_trader',
      name: '传奇商人',
      description: '达到50级并完成所有主线任务',
      icon: '👑',
      rarity: 'legendary',
      category: 'achievement'
    },
    herb_sage: {
      id: 'herb_sage',
      name: '草本贤者',
      description: '收集所有四种经典草本：芜荽、鼠尾草、迷迭香、百里香',
      icon: '🧙‍♂️',
      rarity: 'epic',
      category: 'collection'
    },
    social_butterfly: {
      id: 'social_butterfly',
      name: '社交达人',
      description: '获得100个日记点赞',
      icon: '🦋',
      rarity: 'uncommon',
      category: 'social'
    },
    night_owl: {
      id: 'night_owl',
      name: '夜猫子',
      description: '在深夜时分完成10次交易',
      icon: '🦉',
      rarity: 'uncommon',
      category: 'special'
    }
  }

  const getRarityColor = (rarity) => {
    switch (rarity) {
      case 'common':
        return 'bg-gray-100 text-gray-700 border-gray-300'
      case 'uncommon':
        return 'bg-green-100 text-green-700 border-green-300'
      case 'rare':
        return 'bg-blue-100 text-blue-700 border-blue-300'
      case 'epic':
        return 'bg-purple-100 text-purple-700 border-purple-300'
      case 'legendary':
        return 'bg-yellow-100 text-yellow-700 border-yellow-300'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-300'
    }
  }

  const getRarityLabel = (rarity) => {
    switch (rarity) {
      case 'common':
        return '普通'
      case 'uncommon':
        return '稀有'
      case 'rare':
        return '珍贵'
      case 'epic':
        return '史诗'
      case 'legendary':
        return '传奇'
      default:
        return '普通'
    }
  }

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'achievement':
        return <Trophy size={16} />
      case 'collection':
        return <Star size={16} />
      case 'social':
        return <Award size={16} />
      case 'exploration':
        return <Target size={16} />
      case 'business':
        return <Award size={16} />
      case 'special':
        return <Star size={16} />
      default:
        return <Award size={16} />
    }
  }

  // 获取用户已解锁的徽章
  const unlockedBadges = badges.map(badge => ({
    ...badgeDefinitions[badge.id],
    ...badge,
    unlocked: true
  }))

  // 获取所有徽章（包括未解锁的）
  const allBadges = Object.values(badgeDefinitions).map(badgeDef => {
    const userBadge = badges.find(b => b.id === badgeDef.id)
    return {
      ...badgeDef,
      ...userBadge,
      unlocked: !!userBadge
    }
  })

  const displayBadges = showAll ? allBadges : unlockedBadges

  const filteredBadges = filter === 'all' 
    ? displayBadges 
    : displayBadges.filter(badge => badge.category === filter)

  const categories = [
    { value: 'all', label: '全部' },
    { value: 'achievement', label: '成就' },
    { value: 'collection', label: '收集' },
    { value: 'social', label: '社交' },
    { value: 'exploration', label: '探索' },
    { value: 'business', label: '商业' },
    { value: 'special', label: '特殊' }
  ]

  return (
    <div className="space-y-4">
      {/* 过滤器 */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <button
            key={category.value}
            onClick={() => setFilter(category.value)}
            className={`px-3 py-1 text-sm rounded-lg transition-colors duration-200 ${
              filter === category.value
                ? 'bg-herb-green text-parchment'
                : 'bg-sage-gray/20 text-sage-gray hover:bg-herb-green/20'
            }`}
          >
            {category.label}
          </button>
        ))}
      </div>

      {/* 徽章统计 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-3 bg-herb-green/10 rounded-lg">
          <div className="text-2xl font-bold text-herb-green">{unlockedBadges.length}</div>
          <div className="text-sm text-sage-gray">已获得</div>
        </div>
        <div className="text-center p-3 bg-sage-gray/10 rounded-lg">
          <div className="text-2xl font-bold text-sage-gray">{Object.keys(badgeDefinitions).length - unlockedBadges.length}</div>
          <div className="text-sm text-sage-gray">未解锁</div>
        </div>
        <div className="text-center p-3 bg-rosemary-purple/10 rounded-lg">
          <div className="text-2xl font-bold text-rosemary-purple">
            {Math.round((unlockedBadges.length / Object.keys(badgeDefinitions).length) * 100)}%
          </div>
          <div className="text-sm text-sage-gray">完成度</div>
        </div>
        <div className="text-center p-3 bg-herb-gold/10 rounded-lg">
          <div className="text-2xl font-bold text-herb-gold">
            {unlockedBadges.filter(b => b.rarity === 'legendary' || b.rarity === 'epic').length}
          </div>
          <div className="text-sm text-sage-gray">稀有徽章</div>
        </div>
      </div>

      {/* 徽章网格 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {filteredBadges.map((badge, index) => (
          <div
            key={badge.id}
            onClick={() => setSelectedBadge(badge)}
            className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:scale-105 ${
              badge.unlocked
                ? `${getRarityColor(badge.rarity)} hover:shadow-lg`
                : 'bg-sage-gray/10 text-sage-gray border-sage-gray/30 hover:bg-sage-gray/20'
            }`}
          >
            {/* 徽章图标 */}
            <div className="text-center mb-3">
              <div className={`text-4xl mb-2 ${badge.unlocked ? '' : 'grayscale opacity-50'}`}>
                {badge.unlocked ? badge.icon : '🔒'}
              </div>
              <h3 className={`font-medieval text-sm ${badge.unlocked ? '' : 'text-sage-gray'}`}>
                {badge.name}
              </h3>
            </div>

            {/* 稀有度标签 */}
            {badge.unlocked && (
              <div className="absolute top-2 right-2">
                <span className={`text-xs px-2 py-1 rounded-full ${getRarityColor(badge.rarity)}`}>
                  {getRarityLabel(badge.rarity)}
                </span>
              </div>
            )}

            {/* 分类图标 */}
            <div className="absolute bottom-2 left-2">
              <div className={`p-1 rounded ${badge.unlocked ? 'text-current' : 'text-sage-gray'}`}>
                {getCategoryIcon(badge.category)}
              </div>
            </div>

            {/* 解锁时间 */}
            {badge.unlocked && badge.unlocked_at && (
              <div className="absolute bottom-2 right-2">
                <Calendar size={12} className="text-current opacity-60" />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 徽章详情模态框 */}
      {selectedBadge && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-parchment rounded-lg max-w-md w-full p-6">
            <div className="text-center mb-4">
              <div className={`text-6xl mb-3 ${selectedBadge.unlocked ? '' : 'grayscale opacity-50'}`}>
                {selectedBadge.unlocked ? selectedBadge.icon : '🔒'}
              </div>
              <h2 className="text-2xl font-medieval text-herb-green mb-2">
                {selectedBadge.name}
              </h2>
              <div className={`inline-block px-3 py-1 rounded-full text-sm ${getRarityColor(selectedBadge.rarity)}`}>
                {getRarityLabel(selectedBadge.rarity)}
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-herb-green mb-2">描述</h3>
                <p className="text-sage-gray text-sm leading-relaxed">
                  {selectedBadge.description}
                </p>
              </div>

              {selectedBadge.unlocked && selectedBadge.unlocked_at && (
                <div>
                  <h3 className="font-medium text-herb-green mb-2">解锁时间</h3>
                  <p className="text-sage-gray text-sm">
                    {formatTime(selectedBadge.unlocked_at)}
                  </p>
                </div>
              )}

              {!selectedBadge.unlocked && (
                <div className="p-3 bg-sage-gray/10 rounded-lg">
                  <h3 className="font-medium text-sage-gray mb-2">解锁条件</h3>
                  <p className="text-sage-gray text-sm">
                    {selectedBadge.description}
                  </p>
                </div>
              )}
            </div>

            <button
              onClick={() => setSelectedBadge(null)}
              className="w-full mt-6 btn-medieval"
            >
              关闭
            </button>
          </div>
        </div>
      )}

      {/* 空状态 */}
      {filteredBadges.length === 0 && (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🏆</div>
          <h3 className="text-xl font-medieval text-herb-green mb-2">
            {filter === 'all' ? '还没有徽章' : '该分类下暂无徽章'}
          </h3>
          <p className="text-sage-gray">
            {filter === 'all' 
              ? '完成任务和探索来解锁你的第一个徽章吧！' 
              : '尝试其他分类或完成更多活动来解锁徽章'}
          </p>
        </div>
      )}
    </div>
  )
}

export default BadgeCollection
