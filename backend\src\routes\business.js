import express from 'express'
import { authMiddleware } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { supabase } from '../services/supabase.js'

const router = express.Router()

// 获取商业模式配置
router.get('/models', asyncHandler(async (req, res) => {
  const businessModels = [
    {
      id: 'traditional',
      name: '传统商户',
      description: '专注于单一商品类别的传统经营模式',
      features: [
        '专业化经营',
        '深度商品知识',
        '稳定客户群体',
        '品牌专业度高'
      ],
      requirements: {
        min_level: 5,
        initial_investment: 1000, // 香草币
        monthly_fee: 50
      },
      benefits: {
        commission_rate: 0.05, // 5%
        marketing_support: true,
        priority_listing: false
      }
    },
    {
      id: 'hybrid',
      name: '混合经营',
      description: '结合线上线下的创新经营模式',
      features: [
        '多渠道销售',
        '灵活库存管理',
        '数据驱动决策',
        '客户体验优化'
      ],
      requirements: {
        min_level: 10,
        initial_investment: 2000,
        monthly_fee: 100
      },
      benefits: {
        commission_rate: 0.04, // 4%
        marketing_support: true,
        priority_listing: true,
        analytics_access: true
      }
    },
    {
      id: 'franchise',
      name: '特许经营',
      description: '加盟知名品牌，享受品牌效应和支持',
      features: [
        '品牌授权',
        '统一管理',
        '培训支持',
        '供应链优势'
      ],
      requirements: {
        min_level: 15,
        initial_investment: 5000,
        monthly_fee: 200
      },
      benefits: {
        commission_rate: 0.03, // 3%
        marketing_support: true,
        priority_listing: true,
        analytics_access: true,
        brand_protection: true
      }
    },
    {
      id: 'cooperative',
      name: '合作社模式',
      description: '多个商户联合经营，共享资源和风险',
      features: [
        '资源共享',
        '风险分担',
        '集体采购',
        '互助支持'
      ],
      requirements: {
        min_level: 8,
        initial_investment: 500,
        monthly_fee: 30,
        min_members: 3
      },
      benefits: {
        commission_rate: 0.045, // 4.5%
        marketing_support: true,
        priority_listing: false,
        bulk_discount: true
      }
    }
  ]

  res.json({
    success: true,
    data: businessModels
  })
}))

// 申请商业模式
router.post('/apply', authMiddleware, asyncHandler(async (req, res) => {
  const { model_id, business_plan, additional_info } = req.body

  // 检查用户是否已有商户
  const { data: existingMerchant } = await supabase
    .from('merchants')
    .select('id, business_model')
    .eq('user_id', req.user.id)
    .single()

  if (!existingMerchant) {
    return res.status(400).json({
      success: false,
      error: '请先创建商户账户'
    })
  }

  // 检查用户等级和资格
  const { data: userProfile } = await supabase
    .from('users')
    .select('level, vanilla_coins')
    .eq('id', req.user.id)
    .single()

  // 获取商业模式要求
  const modelRequirements = {
    traditional: { min_level: 5, initial_investment: 1000 },
    hybrid: { min_level: 10, initial_investment: 2000 },
    franchise: { min_level: 15, initial_investment: 5000 },
    cooperative: { min_level: 8, initial_investment: 500 }
  }

  const requirements = modelRequirements[model_id]
  if (!requirements) {
    return res.status(400).json({
      success: false,
      error: '无效的商业模式'
    })
  }

  // 检查等级要求
  if (userProfile.level < requirements.min_level) {
    return res.status(400).json({
      success: false,
      error: `需要达到 ${requirements.min_level} 级才能申请此模式`
    })
  }

  // 检查资金要求
  if (userProfile.vanilla_coins < requirements.initial_investment) {
    return res.status(400).json({
      success: false,
      error: `需要 ${requirements.initial_investment} 香草币作为初始投资`
    })
  }

  // 创建申请记录
  const { data: application, error } = await supabase
    .from('business_applications')
    .insert({
      user_id: req.user.id,
      merchant_id: existingMerchant.id,
      model_id,
      business_plan,
      additional_info,
      status: 'pending',
      applied_at: new Date().toISOString()
    })
    .select()
    .single()

  if (error) throw error

  // 扣除申请费用（初始投资的10%作为申请费）
  const applicationFee = Math.floor(requirements.initial_investment * 0.1)
  await supabase.rpc('add_vanilla_coins', {
    user_id: req.user.id,
    coin_amount: -applicationFee
  })

  // 发送申请通知
  await supabase
    .from('messages')
    .insert({
      sender_id: req.user.id,
      recipient_id: req.user.id,
      title: '商业模式申请已提交',
      content: `您的 ${model_id} 模式申请已提交，我们将在3-5个工作日内审核。申请费 ${applicationFee} 香草币已扣除。`,
      type: 'system'
    })

  res.status(201).json({
    success: true,
    data: application,
    message: '申请已提交，请等待审核'
  })
}))

// 获取用户的商业模式申请
router.get('/applications', authMiddleware, asyncHandler(async (req, res) => {
  const { data: applications, error } = await supabase
    .from('business_applications')
    .select('*')
    .eq('user_id', req.user.id)
    .order('applied_at', { ascending: false })

  if (error) throw error

  res.json({
    success: true,
    data: applications
  })
}))

// 获取商业分析数据
router.get('/analytics', authMiddleware, asyncHandler(async (req, res) => {
  const { timeframe = '30d' } = req.query

  // 获取商户信息
  const { data: merchant } = await supabase
    .from('merchants')
    .select('id, business_model')
    .eq('user_id', req.user.id)
    .single()

  if (!merchant) {
    return res.status(404).json({
      success: false,
      error: '商户不存在'
    })
  }

  // 计算时间范围
  const days = timeframe === '7d' ? 7 : timeframe === '30d' ? 30 : 90
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)

  // 并行查询各种数据
  const [
    { data: salesData },
    { data: orderData },
    { data: productData },
    { data: customerData }
  ] = await Promise.all([
    // 销售数据
    supabase
      .from('orders')
      .select('total_price, created_at')
      .eq('merchant_id', merchant.id)
      .eq('payment_status', 'completed')
      .gte('created_at', startDate.toISOString()),
    
    // 订单数据
    supabase
      .from('orders')
      .select('id, order_status, created_at')
      .eq('merchant_id', merchant.id)
      .gte('created_at', startDate.toISOString()),
    
    // 商品数据
    supabase
      .from('products')
      .select('id, category, stock_quantity, created_at')
      .eq('merchant_id', merchant.id),
    
    // 客户数据
    supabase
      .from('orders')
      .select('user_id, created_at')
      .eq('merchant_id', merchant.id)
      .gte('created_at', startDate.toISOString())
  ])

  // 计算关键指标
  const totalRevenue = salesData?.reduce((sum, order) => sum + parseFloat(order.total_price), 0) || 0
  const totalOrders = orderData?.length || 0
  const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0
  const uniqueCustomers = new Set(customerData?.map(order => order.user_id)).size

  // 按日期分组的销售数据
  const dailySales = {}
  salesData?.forEach(order => {
    const date = order.created_at.split('T')[0]
    dailySales[date] = (dailySales[date] || 0) + parseFloat(order.total_price)
  })

  // 商品类别分析
  const categoryStats = {}
  productData?.forEach(product => {
    const category = product.category
    if (!categoryStats[category]) {
      categoryStats[category] = { count: 0, totalStock: 0 }
    }
    categoryStats[category].count++
    categoryStats[category].totalStock += product.stock_quantity
  })

  // 生成商业建议
  const suggestions = generateBusinessSuggestions({
    totalRevenue,
    totalOrders,
    averageOrderValue,
    uniqueCustomers,
    businessModel: merchant.business_model,
    timeframe: days
  })

  res.json({
    success: true,
    data: {
      summary: {
        totalRevenue: totalRevenue.toFixed(2),
        totalOrders,
        averageOrderValue: averageOrderValue.toFixed(2),
        uniqueCustomers,
        timeframe: `${days}天`
      },
      trends: {
        dailySales: Object.entries(dailySales).map(([date, revenue]) => ({
          date,
          revenue: parseFloat(revenue.toFixed(2))
        }))
      },
      categories: Object.entries(categoryStats).map(([category, stats]) => ({
        category,
        productCount: stats.count,
        totalStock: stats.totalStock
      })),
      suggestions
    }
  })
}))

// 生成商业建议
function generateBusinessSuggestions({ totalRevenue, totalOrders, averageOrderValue, uniqueCustomers, businessModel, timeframe }) {
  const suggestions = []

  // 基于收入的建议
  if (totalRevenue < 1000) {
    suggestions.push({
      type: 'revenue',
      priority: 'high',
      title: '提升销售额',
      description: '考虑增加商品种类或参与更多集市活动来提升销售额',
      action: '查看热门商品分类，考虑添加相关商品'
    })
  }

  // 基于订单量的建议
  if (totalOrders < 10) {
    suggestions.push({
      type: 'orders',
      priority: 'medium',
      title: '增加订单量',
      description: '通过优化商品描述和图片来吸引更多客户',
      action: '更新商品图片和描述，使用更吸引人的标题'
    })
  }

  // 基于客单价的建议
  if (averageOrderValue < 50) {
    suggestions.push({
      type: 'aov',
      priority: 'medium',
      title: '提升客单价',
      description: '考虑推出套装商品或提供批量折扣',
      action: '创建商品套装，设置满额优惠活动'
    })
  }

  // 基于商业模式的建议
  if (businessModel === 'traditional') {
    suggestions.push({
      type: 'model',
      priority: 'low',
      title: '考虑升级模式',
      description: '您可能适合升级到混合经营模式以获得更多功能',
      action: '了解混合经营模式的优势和要求'
    })
  }

  return suggestions
}

// 商业模式升级
router.post('/upgrade', authMiddleware, asyncHandler(async (req, res) => {
  const { target_model } = req.body

  // 获取当前商户信息
  const { data: merchant } = await supabase
    .from('merchants')
    .select('id, business_model')
    .eq('user_id', req.user.id)
    .single()

  if (!merchant) {
    return res.status(404).json({
      success: false,
      error: '商户不存在'
    })
  }

  // 检查升级路径
  const upgradeMatrix = {
    traditional: ['hybrid', 'cooperative'],
    hybrid: ['franchise'],
    cooperative: ['hybrid'],
    franchise: [] // 最高级别
  }

  if (!upgradeMatrix[merchant.business_model]?.includes(target_model)) {
    return res.status(400).json({
      success: false,
      error: '无效的升级路径'
    })
  }

  // 计算升级费用
  const upgradeFees = {
    'traditional->hybrid': 1000,
    'traditional->cooperative': 500,
    'hybrid->franchise': 3000,
    'cooperative->hybrid': 1500
  }

  const upgradeKey = `${merchant.business_model}->${target_model}`
  const upgradeFee = upgradeFees[upgradeKey] || 0

  // 检查用户资金
  const { data: userProfile } = await supabase
    .from('users')
    .select('vanilla_coins')
    .eq('id', req.user.id)
    .single()

  if (userProfile.vanilla_coins < upgradeFee) {
    return res.status(400).json({
      success: false,
      error: `升级需要 ${upgradeFee} 香草币`
    })
  }

  // 执行升级
  await supabase
    .from('merchants')
    .update({ 
      business_model: target_model,
      updated_at: new Date().toISOString()
    })
    .eq('id', merchant.id)

  // 扣除升级费用
  await supabase.rpc('add_vanilla_coins', {
    user_id: req.user.id,
    coin_amount: -upgradeFee
  })

  // 记录升级历史
  await supabase
    .from('business_upgrades')
    .insert({
      merchant_id: merchant.id,
      from_model: merchant.business_model,
      to_model: target_model,
      upgrade_fee: upgradeFee,
      upgraded_at: new Date().toISOString()
    })

  // 发送升级通知
  await supabase
    .from('messages')
    .insert({
      sender_id: req.user.id,
      recipient_id: req.user.id,
      title: '商业模式升级成功',
      content: `恭喜！您的商业模式已成功升级为 ${target_model}。新功能将在24小时内生效。`,
      type: 'system'
    })

  res.json({
    success: true,
    message: '升级成功',
    data: {
      old_model: merchant.business_model,
      new_model: target_model,
      upgrade_fee: upgradeFee
    }
  })
}))

export default router
