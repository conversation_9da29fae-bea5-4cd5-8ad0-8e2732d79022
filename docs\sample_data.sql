-- 斯卡布罗集市示例数据
-- 用于开发和测试的初始数据

-- 插入示例用户 (需要先在 Supabase Auth 中创建对应用户)
-- 这里使用示例 UUID，实际使用时需要替换为真实的 Auth 用户 ID

-- 示例商户数据
INSERT INTO merchants (id, user_id, name, description, location, contact_info, business_hours) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************', -- 需要替换为真实用户ID
    '芙萝拉的草本花园',
    '专营各种新鲜草本植物和香料，传承古老的草本智慧',
    '斯卡布罗集市东区',
    '{"phone": "138-0000-0001", "email": "<EMAIL>"}',
    '{"monday": "9:00-18:00", "tuesday": "9:00-18:00", "wednesday": "9:00-18:00", "thursday": "9:00-18:00", "friday": "9:00-18:00", "saturday": "8:00-19:00", "sunday": "10:00-17:00"}'
),
(
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '凯恩的狩猎小屋',
    '提供优质狩猎装备和野味，冒险者的最佳选择',
    '斯卡布罗集市北区',
    '{"phone": "138-0000-0002", "email": "<EMAIL>"}',
    '{"monday": "10:00-20:00", "tuesday": "10:00-20:00", "wednesday": "10:00-20:00", "thursday": "10:00-20:00", "friday": "10:00-20:00", "saturday": "9:00-21:00", "sunday": "休息"}'
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-************',
    '莱瑞克的音乐工坊',
    '手工制作各种乐器，传播音乐的魅力',
    '斯卡布罗集市中央广场',
    '{"phone": "138-0000-0003", "email": "<EMAIL>"}',
    '{"monday": "11:00-19:00", "tuesday": "11:00-19:00", "wednesday": "11:00-19:00", "thursday": "11:00-19:00", "friday": "11:00-19:00", "saturday": "10:00-20:00", "sunday": "12:00-18:00"}'
);

-- 示例商品数据
INSERT INTO products (merchant_id, name, description, price, category, availability_conditions, stock_quantity, tags) VALUES
-- 芙萝拉的草本花园商品
(
    '550e8400-e29b-41d4-a716-************',
    '新鲜芜荽',
    '来自斯卡布罗田野的新鲜芜荽，带有独特的香气，是制作香料的绝佳选择',
    15.50,
    '草本香料',
    '{"weather": ["sunny", "cloudy"], "season": ["spring", "summer"]}',
    50,
    ARRAY['芜荽', '香料', '新鲜', '有机']
),
(
    '550e8400-e29b-41d4-a716-************',
    '干燥鼠尾草',
    '精心干燥的鼠尾草叶，保持了完整的药用价值和芳香',
    28.00,
    '草本香料',
    '{"weather": ["any"], "season": ["any"]}',
    30,
    ARRAY['鼠尾草', '干燥', '药用', '芳香']
),
(
    '550e8400-e29b-41d4-a716-************',
    '迷迭香精油',
    '纯天然提取的迷迭香精油，具有提神醒脑的功效',
    45.00,
    '精油护理',
    '{"weather": ["any"], "season": ["any"]}',
    20,
    ARRAY['迷迭香', '精油', '天然', '护理']
),
(
    '550e8400-e29b-41d4-a716-************',
    '百里香蜂蜜',
    '蜜蜂采集百里香花蜜制成的天然蜂蜜，口感清香甘甜',
    38.80,
    '天然食品',
    '{"weather": ["sunny"], "season": ["spring", "summer"]}',
    25,
    ARRAY['百里香', '蜂蜜', '天然', '甜品']
),

-- 凯恩的狩猎小屋商品
(
    '550e8400-e29b-41d4-a716-************',
    '精制猎弓',
    '凯恩亲手制作的复合弓，适合各种狩猎场景',
    280.00,
    '狩猎装备',
    '{"weather": ["any"], "season": ["any"]}',
    8,
    ARRAY['弓箭', '狩猎', '手工', '精制']
),
(
    '550e8400-e29b-41d4-a716-************',
    '野鹿肉干',
    '新鲜野鹿肉制成的肉干，富含蛋白质，是冒险者的理想食物',
    55.00,
    '野味食品',
    '{"weather": ["any"], "season": ["autumn", "winter"]}',
    15,
    ARRAY['野味', '肉干', '蛋白质', '冒险']
),
(
    '550e8400-e29b-41d4-a716-************',
    '狩猎背包',
    '专为长途狩猎设计的多功能背包，容量大且耐用',
    120.00,
    '狩猎装备',
    '{"weather": ["any"], "season": ["any"]}',
    12,
    ARRAY['背包', '狩猎', '耐用', '多功能']
),

-- 莱瑞克的音乐工坊商品
(
    '550e8400-e29b-41d4-a716-446655440003',
    '手工鲁特琴',
    '莱瑞克精心制作的鲁特琴，音色优美，是吟游诗人的最爱',
    450.00,
    '乐器',
    '{"weather": ["any"], "season": ["any"]}',
    5,
    ARRAY['鲁特琴', '手工', '乐器', '音乐']
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    '竹制长笛',
    '选用优质竹材制作的长笛，音色清脆悦耳',
    85.00,
    '乐器',
    '{"weather": ["any"], "season": ["any"]}',
    10,
    ARRAY['长笛', '竹制', '乐器', '清脆']
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    '音乐盒',
    '精美的手工音乐盒，播放经典的斯卡布罗集市旋律',
    68.00,
    '工艺品',
    '{"weather": ["any"], "season": ["any"]}',
    15,
    ARRAY['音乐盒', '手工', '工艺品', '旋律']
);

-- 示例任务数据
INSERT INTO tasks (user_id, title, description, type, organization, rewards, conditions, max_progress) VALUES
(
    '550e8400-e29b-41d4-a716-************', -- 需要替换为真实用户ID
    '初来乍到',
    '欢迎来到斯卡布罗集市！完成你的第一次购买来熟悉集市的运作方式。',
    'daily',
    '集市管理委员会',
    '{"xp": 50, "coins": 100, "badges": [{"id": "first_purchase", "name": "初次购买"}]}',
    '{"type": "purchase", "count": 1}',
    1
),
(
    '550e8400-e29b-41d4-a716-************',
    '草本收集者',
    '从芙萝拉的花园购买任意三种不同的草本植物。',
    'weekly',
    '草本研究协会',
    '{"xp": 100, "coins": 200, "badges": [{"id": "herb_collector", "name": "草本收集者"}]}',
    '{"type": "purchase_category", "category": "草本香料", "count": 3}',
    3
),
(
    '550e8400-e29b-41d4-a716-************',
    '日记作家',
    '写下你在集市的见闻，发布5篇日记来记录你的冒险。',
    'weekly',
    '文学爱好者协会',
    '{"xp": 150, "coins": 150, "badges": [{"id": "diary_writer", "name": "日记作家"}]}',
    '{"type": "diary_entries", "count": 5}',
    5
),
(
    '550e8400-e29b-41d4-a716-************',
    '探索者',
    '解锁集市地图上的3个新地点。',
    'special',
    '探险家公会',
    '{"xp": 200, "coins": 300, "badges": [{"id": "explorer", "name": "探索者"}]}',
    '{"type": "unlock_locations", "count": 3}',
    3
);

-- 示例事件数据
INSERT INTO events (title, description, type, trigger_conditions, effects, rewards, start_time, end_time) VALUES
(
    '晴日集市',
    '阳光明媚的日子里，所有草本植物的价格下降10%',
    'weather',
    '{"weather": "sunny"}',
    '{"discount": {"category": "草本香料", "percentage": 10}}',
    '{"xp_bonus": 20}',
    NOW(),
    NOW() + INTERVAL '1 day'
),
(
    '雨夜诗会',
    '雨夜时分，发布日记可获得额外经验值',
    'weather',
    '{"weather": "rainy", "time": "night"}',
    '{"xp_multiplier": {"action": "diary_post", "multiplier": 2}}',
    '{"coins": 50}',
    NOW(),
    NOW() + INTERVAL '12 hours'
),
(
    '月圆狩猎节',
    '满月之夜，狩猎装备销量翻倍，凯恩的小屋特别繁忙',
    'time',
    '{"moon_phase": "full"}',
    '{"sales_boost": {"merchant": "凯恩的狩猎小屋", "multiplier": 2}}',
    '{"xp": 100, "coins": 200}',
    NOW(),
    NOW() + INTERVAL '3 days'
);

-- 示例日记数据
INSERT INTO diary_entries (user_id, title, content, type, visibility, tags) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    '初到斯卡布罗',
    '今天第一次来到斯卡布罗集市，这里的一切都让我感到新奇。芙萝拉的草本花园散发着迷人的香气，莱瑞克的琴声在广场上回荡，凯恩虽然看起来严肃，但他的装备确实精良。这里真的就像歌曲中描述的那样神奇。',
    'vanilla_diary',
    'public',
    ARRAY['初次体验', '集市', '印象']
),
(
    '550e8400-e29b-41d4-a716-************',
    '芜荽的香气',
    '在芙萝拉的花园里，我第一次闻到了真正新鲜芜荽的香气。那种独特的味道让我想起了童年时奶奶做的汤。也许这就是斯卡布罗集市的魅力所在——它不仅仅是一个交易的地方，更是一个承载记忆和情感的空间。',
    'vanilla_diary',
    'public',
    ARRAY['芜荽', '回忆', '童年']
),
(
    '550e8400-e29b-41d4-a716-************',
    '夜雨中的思考',
    '雨滴敲打着窗棂，\n如同时间的脚步声。\n在这个古老的集市里，\n我寻找着什么？\n也许是那失落的自己，\n也许是那未来的可能。\n芜荽、鼠尾草、迷迭香、百里香，\n每一种草本都在诉说着故事。',
    'wanderer_poem',
    'public',
    ARRAY['诗歌', '雨夜', '思考']
);

-- 示例消息数据
INSERT INTO messages (sender_id, recipient_id, title, content, type) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '欢迎来到斯卡布罗集市！',
    '亲爱的冒险者，欢迎来到斯卡布罗集市！这里有最新鲜的草本植物、最精良的狩猎装备，还有最动听的音乐。希望你在这里度过愉快的时光，找到你所寻找的一切。记住，在这里每一次交易都是一次冒险，每一个故事都值得记录。',
    'system'
),
(
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '新手任务提醒',
    '你有一些新手任务等待完成！完成这些任务可以帮助你更好地了解集市的运作方式，同时获得丰厚的奖励。快去任务面板查看吧！',
    'system'
);
