// API 请求工具函数

// 基础 API 配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

// 请求拦截器
const createApiRequest = (token = null) => {
  const headers = {
    'Content-Type': 'application/json',
  }
  
  if (token) {
    headers.Authorization = `Bearer ${token}`
  }
  
  return {
    get: async (url, params = {}) => {
      const queryString = new URLSearchParams(params).toString()
      const fullUrl = `${API_BASE_URL}${url}${queryString ? `?${queryString}` : ''}`
      
      const response = await fetch(fullUrl, {
        method: 'GET',
        headers,
      })
      
      return handleResponse(response)
    },
    
    post: async (url, data = {}) => {
      const response = await fetch(`${API_BASE_URL}${url}`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
      })
      
      return handleResponse(response)
    },
    
    put: async (url, data = {}) => {
      const response = await fetch(`${API_BASE_URL}${url}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(data),
      })
      
      return handleResponse(response)
    },
    
    delete: async (url) => {
      const response = await fetch(`${API_BASE_URL}${url}`, {
        method: 'DELETE',
        headers,
      })
      
      return handleResponse(response)
    }
  }
}

// 响应处理
const handleResponse = async (response) => {
  const data = await response.json()
  
  if (!response.ok) {
    throw new Error(data.error || `HTTP error! status: ${response.status}`)
  }
  
  return data
}

// 用户相关 API
export const userAPI = {
  // 获取用户档案
  getProfile: (token) => {
    const api = createApiRequest(token)
    return api.get('/users/profile')
  },
  
  // 更新用户档案
  updateProfile: (token, data) => {
    const api = createApiRequest(token)
    return api.put('/users/profile', data)
  },
  
  // 获取用户统计
  getStats: (token) => {
    const api = createApiRequest(token)
    return api.get('/users/stats')
  },
  
  // 增加经验值
  addXP: (token, amount, reason) => {
    const api = createApiRequest(token)
    return api.post('/users/xp', { amount, reason })
  },
  
  // 增加香草币
  addCoins: (token, amount, reason) => {
    const api = createApiRequest(token)
    return api.post('/users/coins', { amount, reason })
  },
  
  // 获取徽章
  getBadges: (token) => {
    const api = createApiRequest(token)
    return api.get('/users/badges')
  },
  
  // 获取排行榜
  getLeaderboard: (type = 'level', limit = 10) => {
    const api = createApiRequest()
    return api.get('/users/leaderboard', { type, limit })
  }
}

// 商品相关 API
export const productAPI = {
  // 获取推荐商品
  getRecommendations: (params = {}) => {
    const api = createApiRequest()
    return api.get('/products/recommendations', params)
  },
  
  // 搜索商品
  search: (searchTerm, filters = {}) => {
    const api = createApiRequest()
    return api.get('/products/search', { q: searchTerm, ...filters })
  },
  
  // 获取商品详情
  getDetails: (productId) => {
    const api = createApiRequest()
    return api.get(`/products/${productId}`)
  },
  
  // 获取商品分类
  getCategories: () => {
    const api = createApiRequest()
    return api.get('/products/categories/list')
  },
  
  // 获取热门商品
  getTrending: (params = {}) => {
    const api = createApiRequest()
    return api.get('/products/trending/list', params)
  },
  
  // 获取相似商品
  getSimilar: (productId, limit = 5) => {
    const api = createApiRequest()
    return api.get(`/products/${productId}/similar`, { limit })
  }
}

// 商户相关 API
export const merchantAPI = {
  // 获取商户列表
  getList: (params = {}) => {
    const api = createApiRequest()
    return api.get('/merchants', params)
  },
  
  // 获取商户详情
  getDetails: (merchantId) => {
    const api = createApiRequest()
    return api.get(`/merchants/${merchantId}`)
  },
  
  // 创建商户
  create: (token, data) => {
    const api = createApiRequest(token)
    return api.post('/merchants', data)
  },
  
  // 更新商户信息
  update: (token, merchantId, data) => {
    const api = createApiRequest(token)
    return api.put(`/merchants/${merchantId}`, data)
  },
  
  // 获取商户商品
  getProducts: (merchantId, params = {}) => {
    const api = createApiRequest()
    return api.get(`/merchants/${merchantId}/products`, params)
  },
  
  // 添加商品
  addProduct: (token, merchantId, data) => {
    const api = createApiRequest(token)
    return api.post(`/merchants/${merchantId}/products`, data)
  },
  
  // 获取商户统计
  getStats: (merchantId) => {
    const api = createApiRequest()
    return api.get(`/merchants/${merchantId}/stats`)
  }
}

// 订单相关 API
export const orderAPI = {
  // 获取订单列表
  getList: (token, params = {}) => {
    const api = createApiRequest(token)
    return api.get('/orders', params)
  },
  
  // 获取订单详情
  getDetails: (token, orderId) => {
    const api = createApiRequest(token)
    return api.get(`/orders/${orderId}`)
  },
  
  // 创建订单
  create: (token, data) => {
    const api = createApiRequest(token)
    return api.post('/orders', data)
  },
  
  // 更新订单状态
  updateStatus: (token, orderId, data) => {
    const api = createApiRequest(token)
    return api.put(`/orders/${orderId}/status`, data)
  },
  
  // 取消订单
  cancel: (token, orderId, reason) => {
    const api = createApiRequest(token)
    return api.put(`/orders/${orderId}/cancel`, { reason })
  }
}

// 任务相关 API
export const taskAPI = {
  // 获取任务列表
  getList: (token, params = {}) => {
    const api = createApiRequest(token)
    return api.get('/tasks', params)
  },
  
  // 获取任务详情
  getDetails: (token, taskId) => {
    const api = createApiRequest(token)
    return api.get(`/tasks/${taskId}`)
  },
  
  // 完成任务
  complete: (token, taskId) => {
    const api = createApiRequest(token)
    return api.post(`/tasks/${taskId}/complete`)
  },
  
  // 更新任务进度
  updateProgress: (token, taskId, progress) => {
    const api = createApiRequest(token)
    return api.put(`/tasks/${taskId}/progress`, { progress })
  },
  
  // 获取任务统计
  getStats: (token) => {
    const api = createApiRequest(token)
    return api.get('/tasks/stats/summary')
  }
}

// 日记相关 API
export const diaryAPI = {
  // 获取公开日记
  getPublic: (params = {}) => {
    const api = createApiRequest()
    return api.get('/diary', params)
  },
  
  // 获取我的日记
  getMy: (token, params = {}) => {
    const api = createApiRequest(token)
    return api.get('/diary/my', params)
  },
  
  // 获取日记详情
  getDetails: (diaryId, token = null) => {
    const api = createApiRequest(token)
    return api.get(`/diary/${diaryId}`)
  },
  
  // 创建日记
  create: (token, data) => {
    const api = createApiRequest(token)
    return api.post('/diary', data)
  },
  
  // 更新日记
  update: (token, diaryId, data) => {
    const api = createApiRequest(token)
    return api.put(`/diary/${diaryId}`, data)
  },
  
  // 删除日记
  delete: (token, diaryId) => {
    const api = createApiRequest(token)
    return api.delete(`/diary/${diaryId}`)
  },
  
  // 点赞日记
  like: (token, diaryId) => {
    const api = createApiRequest(token)
    return api.post(`/diary/${diaryId}/like`)
  },
  
  // 添加评论
  addComment: (token, diaryId, content) => {
    const api = createApiRequest(token)
    return api.post(`/diary/${diaryId}/comments`, { content })
  },
  
  // 获取热门日记
  getTrending: (params = {}) => {
    const api = createApiRequest()
    return api.get('/diary/trending/list', params)
  },
  
  // 搜索日记
  search: (searchTerm, params = {}) => {
    const api = createApiRequest()
    return api.get('/diary/search/entries', { q: searchTerm, ...params })
  }
}

// 地图相关 API
export const mapAPI = {
  // 获取地图位置
  getLocations: (token = null) => {
    const api = createApiRequest(token)
    return api.get('/map/locations')
  },
  
  // 获取位置详情
  getLocationDetails: (locationId, token = null) => {
    const api = createApiRequest(token)
    return api.get(`/map/locations/${locationId}`)
  },
  
  // 解锁位置
  unlockLocation: (token, locationId) => {
    const api = createApiRequest(token)
    return api.post(`/map/locations/${locationId}/unlock`)
  },
  
  // 探索位置
  exploreLocation: (token, locationId) => {
    const api = createApiRequest(token)
    return api.post(`/map/locations/${locationId}/explore`)
  },
  
  // 获取探索统计
  getExplorationStats: (token) => {
    const api = createApiRequest(token)
    return api.get('/map/exploration/stats')
  }
}

// 事件相关 API
export const eventAPI = {
  // 获取活跃事件
  getActive: () => {
    const api = createApiRequest()
    return api.get('/events/active')
  },
  
  // 获取所有事件
  getAll: (params = {}) => {
    const api = createApiRequest()
    return api.get('/events', params)
  },
  
  // 获取事件详情
  getDetails: (eventId) => {
    const api = createApiRequest()
    return api.get(`/events/${eventId}`)
  },
  
  // 参与事件
  participate: (token, eventId) => {
    const api = createApiRequest(token)
    return api.post(`/events/${eventId}/participate`)
  },
  
  // 获取事件统计
  getStats: () => {
    const api = createApiRequest()
    return api.get('/events/stats/summary')
  }
}
