-- 斯卡布罗集市数据库函数
-- 游戏化逻辑和业务逻辑函数

-- 增加用户经验值并自动升级
CREATE OR REPLACE FUNCTION add_user_xp(user_id UUID, xp_amount INTEGER)
RETURNS TABLE(new_level INTEGER, new_xp INTEGER, level_up BOOLEAN) AS $$
DECLARE
    current_user RECORD;
    old_level INTEGER;
    new_level_calc INTEGER;
    level_up_flag BOOLEAN := FALSE;
BEGIN
    -- 获取当前用户信息
    SELECT level, xp INTO current_user FROM users WHERE id = user_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION '用户不存在';
    END IF;
    
    old_level := current_user.level;
    
    -- 更新经验值
    UPDATE users 
    SET xp = xp + xp_amount,
        updated_at = NOW()
    WHERE id = user_id;
    
    -- 计算新等级 (每100经验值升1级)
    new_level_calc := FLOOR((current_user.xp + xp_amount) / 100) + 1;
    
    -- 如果等级提升，更新等级并给予奖励
    IF new_level_calc > old_level THEN
        level_up_flag := TRUE;
        
        UPDATE users 
        SET level = new_level_calc,
            vanilla_coins = vanilla_coins + (new_level_calc - old_level) * 50 -- 每升级给50香草币
        WHERE id = user_id;
        
        -- 插入升级通知消息
        INSERT INTO messages (sender_id, recipient_id, title, content, type)
        VALUES (
            user_id, 
            user_id, 
            '恭喜升级！', 
            FORMAT('恭喜你升级到 %s 级！获得 %s 香草币奖励。', new_level_calc, (new_level_calc - old_level) * 50),
            'system'
        );
    END IF;
    
    RETURN QUERY SELECT new_level_calc, current_user.xp + xp_amount, level_up_flag;
END;
$$ LANGUAGE plpgsql;

-- 增加香草币
CREATE OR REPLACE FUNCTION add_vanilla_coins(user_id UUID, coin_amount INTEGER)
RETURNS TABLE(new_balance INTEGER) AS $$
DECLARE
    current_balance INTEGER;
BEGIN
    -- 获取当前余额
    SELECT vanilla_coins INTO current_balance FROM users WHERE id = user_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION '用户不存在';
    END IF;
    
    -- 更新余额
    UPDATE users 
    SET vanilla_coins = vanilla_coins + coin_amount,
        updated_at = NOW()
    WHERE id = user_id;
    
    RETURN QUERY SELECT current_balance + coin_amount;
END;
$$ LANGUAGE plpgsql;

-- 完成任务
CREATE OR REPLACE FUNCTION complete_task(task_id UUID, user_id UUID)
RETURNS TABLE(success BOOLEAN, rewards JSONB) AS $$
DECLARE
    task_record RECORD;
    reward_xp INTEGER;
    reward_coins INTEGER;
    reward_badges JSONB;
BEGIN
    -- 获取任务信息
    SELECT * INTO task_record FROM tasks WHERE id = task_id AND tasks.user_id = user_id AND status = 'active';
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT FALSE, '{}'::jsonb;
        RETURN;
    END IF;
    
    -- 标记任务为完成
    UPDATE tasks 
    SET status = 'completed',
        completed_at = NOW()
    WHERE id = task_id;
    
    -- 处理奖励
    reward_xp := COALESCE((task_record.rewards->>'xp')::INTEGER, 0);
    reward_coins := COALESCE((task_record.rewards->>'coins')::INTEGER, 0);
    reward_badges := COALESCE(task_record.rewards->'badges', '[]'::jsonb);
    
    -- 给予经验值奖励
    IF reward_xp > 0 THEN
        PERFORM add_user_xp(user_id, reward_xp);
    END IF;
    
    -- 给予香草币奖励
    IF reward_coins > 0 THEN
        PERFORM add_vanilla_coins(user_id, reward_coins);
    END IF;
    
    -- 给予徽章奖励
    IF jsonb_array_length(reward_badges) > 0 THEN
        UPDATE users 
        SET badges = badges || reward_badges
        WHERE id = user_id;
    END IF;
    
    -- 插入完成通知
    INSERT INTO messages (sender_id, recipient_id, title, content, type)
    VALUES (
        user_id, 
        user_id, 
        '任务完成！', 
        FORMAT('恭喜完成任务：%s！获得 %s 经验值和 %s 香草币。', task_record.title, reward_xp, reward_coins),
        'system'
    );
    
    RETURN QUERY SELECT TRUE, task_record.rewards;
END;
$$ LANGUAGE plpgsql;

-- 点赞日记
CREATE OR REPLACE FUNCTION like_diary_entry(entry_id UUID, user_id UUID)
RETURNS TABLE(success BOOLEAN, new_like_count INTEGER) AS $$
DECLARE
    current_likes INTEGER;
    entry_author UUID;
BEGIN
    -- 检查是否已经点赞
    IF EXISTS (SELECT 1 FROM diary_likes WHERE diary_entry_id = entry_id AND diary_likes.user_id = user_id) THEN
        -- 取消点赞
        DELETE FROM diary_likes WHERE diary_entry_id = entry_id AND diary_likes.user_id = user_id;
        
        UPDATE diary_entries 
        SET likes = likes - 1 
        WHERE id = entry_id;
        
        SELECT likes INTO current_likes FROM diary_entries WHERE id = entry_id;
        
        RETURN QUERY SELECT TRUE, current_likes;
        RETURN;
    END IF;
    
    -- 添加点赞
    INSERT INTO diary_likes (diary_entry_id, user_id) VALUES (entry_id, user_id);
    
    -- 更新点赞数
    UPDATE diary_entries 
    SET likes = likes + 1 
    WHERE id = entry_id
    RETURNING likes, diary_entries.user_id INTO current_likes, entry_author;
    
    -- 给日记作者增加经验值
    IF entry_author != user_id THEN
        PERFORM add_user_xp(entry_author, 5); -- 获得点赞给5经验值
    END IF;
    
    RETURN QUERY SELECT TRUE, current_likes;
END;
$$ LANGUAGE plpgsql;

-- 创建随机晨雾行者诗篇
CREATE OR REPLACE FUNCTION create_morning_mist_poem(user_id UUID)
RETURNS TABLE(success BOOLEAN, poem_id UUID) AS $$
DECLARE
    new_poem_id UUID;
    poems TEXT[] := ARRAY[
        '晨雾中的脚步声，踏过时间的边界',
        '在斯卡布罗的石径上，我寻找失落的记忆',
        '芜荽的香气飘散，如同昨日的梦境',
        '鼠尾草在风中摇摆，诉说着古老的秘密',
        '迷迭香的蓝色花朵，映照着天空的忧郁',
        '百里香的温暖，如同母亲的怀抱'
    ];
    random_poem TEXT;
BEGIN
    -- 5% 概率触发
    IF random() > 0.05 THEN
        RETURN QUERY SELECT FALSE, NULL::UUID;
        RETURN;
    END IF;
    
    -- 随机选择一首诗
    random_poem := poems[floor(random() * array_length(poems, 1) + 1)];
    
    -- 创建诗篇
    INSERT INTO diary_entries (user_id, title, content, type, is_morning_mist, visibility)
    VALUES (
        user_id,
        '晨雾行者的诗篇',
        random_poem,
        'wanderer_poem',
        TRUE,
        'public'
    )
    RETURNING id INTO new_poem_id;
    
    -- 给用户特殊奖励
    PERFORM add_user_xp(user_id, 100); -- 大量经验值
    PERFORM add_vanilla_coins(user_id, 200); -- 大量香草币
    
    -- 解锁晨雾行者徽章
    UPDATE users 
    SET badges = badges || '[{"id": "morning_mist", "name": "晨雾行者", "unlocked_at": "' || NOW() || '"}]'::jsonb
    WHERE id = user_id AND NOT (badges @> '[{"id": "morning_mist"}]'::jsonb);
    
    -- 发送特殊通知
    INSERT INTO messages (sender_id, recipient_id, title, content, type)
    VALUES (
        user_id, 
        user_id, 
        '晨雾行者的启示', 
        '在晨雾中，你听到了神秘的诗篇。这是晨雾行者留下的痕迹...',
        'system'
    );
    
    RETURN QUERY SELECT TRUE, new_poem_id;
END;
$$ LANGUAGE plpgsql;

-- 处理商品购买
CREATE OR REPLACE FUNCTION purchase_product(
    buyer_id UUID, 
    product_id UUID, 
    quantity INTEGER DEFAULT 1,
    payment_type VARCHAR DEFAULT 'online'
)
RETURNS TABLE(success BOOLEAN, order_id UUID, message TEXT) AS $$
DECLARE
    product_record RECORD;
    merchant_record RECORD;
    total_cost DECIMAL;
    new_order_id UUID;
    user_coins INTEGER;
BEGIN
    -- 获取商品信息
    SELECT p.*, m.user_id as merchant_user_id 
    INTO product_record 
    FROM products p 
    JOIN merchants m ON p.merchant_id = m.id 
    WHERE p.id = product_id AND p.status = 'active';
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, '商品不存在或已下架';
        RETURN;
    END IF;
    
    -- 检查库存
    IF product_record.stock_quantity < quantity THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, '库存不足';
        RETURN;
    END IF;
    
    total_cost := product_record.price * quantity;
    
    -- 如果使用香草币支付，检查余额
    IF payment_type = 'vanilla_coins' THEN
        SELECT vanilla_coins INTO user_coins FROM users WHERE id = buyer_id;
        
        IF user_coins < total_cost THEN
            RETURN QUERY SELECT FALSE, NULL::UUID, '香草币余额不足';
            RETURN;
        END IF;
        
        -- 扣除香草币
        UPDATE users SET vanilla_coins = vanilla_coins - total_cost WHERE id = buyer_id;
        
        -- 给商户增加香草币 (扣除5%手续费)
        UPDATE users 
        SET vanilla_coins = vanilla_coins + (total_cost * 0.95)::INTEGER 
        WHERE id = product_record.merchant_user_id;
    END IF;
    
    -- 创建订单
    INSERT INTO orders (user_id, merchant_id, product_id, quantity, total_price, payment_type, payment_status, order_status)
    VALUES (
        buyer_id, 
        product_record.merchant_id, 
        product_id, 
        quantity, 
        total_cost, 
        payment_type,
        CASE WHEN payment_type = 'vanilla_coins' THEN 'completed' ELSE 'pending' END,
        'pending'
    )
    RETURNING id INTO new_order_id;
    
    -- 更新库存
    UPDATE products 
    SET stock_quantity = stock_quantity - quantity 
    WHERE id = product_id;
    
    -- 给买家增加经验值
    PERFORM add_user_xp(buyer_id, 10 * quantity);
    
    -- 更新商户销售统计
    UPDATE merchants 
    SET total_sales = total_sales + quantity 
    WHERE id = product_record.merchant_id;
    
    RETURN QUERY SELECT TRUE, new_order_id, '订单创建成功';
END;
$$ LANGUAGE plpgsql;
