import React, { useState } from 'react'
import { 
  CheckCircle, 
  Clock, 
  Star, 
  Coins, 
  Award,
  Calendar,
  Target,
  TrendingUp
} from 'lucide-react'

const TaskCard = ({ task, user, onComplete, onUpdateProgress }) => {
  const [isCompleting, setIsCompleting] = useState(false)

  const getTaskTypeIcon = (type) => {
    switch (type) {
      case 'daily':
        return <Calendar size={16} className="text-herb-gold" />
      case 'weekly':
        return <TrendingUp size={16} className="text-rosemary-purple" />
      case 'special':
        return <Star size={16} className="text-herb-gold" />
      case 'exploration':
        return <Target size={16} className="text-herb-green" />
      default:
        return <Clock size={16} className="text-sage-gray" />
    }
  }

  const getTaskTypeLabel = (type) => {
    switch (type) {
      case 'daily':
        return '每日任务'
      case 'weekly':
        return '周任务'
      case 'special':
        return '特殊任务'
      case 'exploration':
        return '探索任务'
      default:
        return '任务'
    }
  }

  const getProgressPercentage = () => {
    return Math.min((task.progress / task.max_progress) * 100, 100)
  }

  const isCompleted = task.status === 'completed'
  const isExpired = task.expires_at && new Date(task.expires_at) < new Date()
  const canComplete = task.progress >= task.max_progress && !isCompleted

  const handleComplete = async () => {
    if (!canComplete || isCompleting) return
    
    setIsCompleting(true)
    try {
      await onComplete(task.id)
    } catch (error) {
      console.error('完成任务失败:', error)
    } finally {
      setIsCompleting(false)
    }
  }

  const formatTimeRemaining = (expiresAt) => {
    if (!expiresAt) return null
    
    const now = new Date()
    const expiry = new Date(expiresAt)
    const diff = expiry - now
    
    if (diff <= 0) return '已过期'
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days}天后过期`
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟后过期`
    } else {
      return `${minutes}分钟后过期`
    }
  }

  return (
    <div className={`card-medieval ${isCompleted ? 'opacity-75' : ''} ${isExpired ? 'border-red-300' : ''}`}>
      {/* 任务头部 */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-2">
          {getTaskTypeIcon(task.type)}
          <span className="text-sm font-medium text-sage-gray">
            {getTaskTypeLabel(task.type)}
          </span>
          {task.organization && (
            <span className="text-xs bg-herb-green/20 text-herb-green px-2 py-1 rounded">
              {task.organization}
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {isCompleted && (
            <CheckCircle size={20} className="text-green-500" />
          )}
          {isExpired && !isCompleted && (
            <Clock size={20} className="text-red-500" />
          )}
        </div>
      </div>

      {/* 任务标题和描述 */}
      <div className="mb-4">
        <h3 className={`text-lg font-medieval mb-2 ${
          isCompleted ? 'text-sage-gray line-through' : 'text-herb-green'
        }`}>
          {task.title}
        </h3>
        <p className="text-sm text-sage-gray leading-relaxed">
          {task.description}
        </p>
      </div>

      {/* 进度条 */}
      {!isCompleted && (
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-sage-gray">进度</span>
            <span className="text-sm font-medium text-herb-green">
              {task.progress} / {task.max_progress}
            </span>
          </div>
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${getProgressPercentage()}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* 奖励信息 */}
      {task.rewards && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-sage-gray mb-2">奖励</h4>
          <div className="flex flex-wrap gap-2">
            {task.rewards.xp && (
              <div className="flex items-center space-x-1 text-sm bg-herb-green/10 text-herb-green px-2 py-1 rounded">
                <Star size={14} />
                <span>{task.rewards.xp} XP</span>
              </div>
            )}
            {task.rewards.coins && (
              <div className="flex items-center space-x-1 text-sm bg-herb-gold/10 text-herb-gold px-2 py-1 rounded">
                <Coins size={14} />
                <span>{task.rewards.coins} 香草币</span>
              </div>
            )}
            {task.rewards.badges && task.rewards.badges.length > 0 && (
              <div className="flex items-center space-x-1 text-sm bg-rosemary-purple/10 text-rosemary-purple px-2 py-1 rounded">
                <Award size={14} />
                <span>徽章</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 时间信息 */}
      {task.expires_at && (
        <div className="mb-4">
          <div className={`text-xs ${isExpired ? 'text-red-500' : 'text-sage-gray'}`}>
            {formatTimeRemaining(task.expires_at)}
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex space-x-2">
        {isCompleted ? (
          <div className="flex-1 text-center py-2 bg-green-100 text-green-700 rounded-lg">
            ✓ 已完成
          </div>
        ) : isExpired ? (
          <div className="flex-1 text-center py-2 bg-red-100 text-red-700 rounded-lg">
            ⏰ 已过期
          </div>
        ) : canComplete ? (
          <button
            onClick={handleComplete}
            disabled={isCompleting}
            className="flex-1 btn-medieval disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isCompleting ? '完成中...' : '完成任务'}
          </button>
        ) : (
          <div className="flex-1 text-center py-2 bg-sage-gray/20 text-sage-gray rounded-lg">
            进行中...
          </div>
        )}
        
        {/* 查看详情按钮 */}
        <button className="px-4 py-2 border border-sage-gray text-sage-gray rounded-lg hover:bg-sage-gray/20 transition-colors duration-200">
          详情
        </button>
      </div>

      {/* 任务条件提示 */}
      {task.conditions && !isCompleted && (
        <div className="mt-3 p-3 bg-herb-green/5 rounded-lg border-l-4 border-herb-green">
          <h5 className="text-sm font-medium text-herb-green mb-1">完成条件</h5>
          <p className="text-xs text-sage-gray">
            {task.conditions.type === 'purchase' && '完成购买'}
            {task.conditions.type === 'diary_entries' && '发布日记'}
            {task.conditions.type === 'unlock_locations' && '解锁地点'}
            {task.conditions.type === 'like_diary' && '点赞日记'}
            {task.conditions.type === 'view_products' && '浏览商品'}
            {task.conditions.count && ` (${task.conditions.count}次)`}
          </p>
        </div>
      )}
    </div>
  )
}

export default TaskCard
