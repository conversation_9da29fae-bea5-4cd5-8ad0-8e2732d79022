import express from 'express'
import { authMiddleware, optionalAuth } from '../middleware/auth.js'
import { asyncHand<PERSON> } from '../middleware/errorHandler.js'
import { supabase } from '../services/supabase.js'

const router = express.Router()

// 预定义的地图位置
const MAP_LOCATIONS = {
  'scarborough': {
    id: 'scarborough',
    name: '斯卡布罗集市',
    description: '主要的集市广场，商户云集',
    coordinates: [39.9042, 116.4074],
    type: 'market',
    unlocked: true,
    unlock_condition: null,
    rewards: { xp: 0, coins: 0 },
    merchants_count: 15,
    special_items: ['香草', '古董', '手工艺品']
  },
  'solitude': {
    id: 'solitude',
    name: '独孤城',
    description: '神秘的古城，隐藏着珍贵的宝物',
    coordinates: [39.9142, 116.4174],
    type: 'city',
    unlocked: false,
    unlock_condition: { type: 'level', value: 10 },
    rewards: { xp: 200, coins: 500 },
    merchants_count: 5,
    special_items: ['稀有装备', '古籍']
  },
  'forgotten_lands': {
    id: 'forgotten_lands',
    name: '遗忘之地',
    description: '被时间遗忘的土地，充满未知的机遇',
    coordinates: [39.8942, 116.3974],
    type: 'forgotten',
    unlocked: false,
    unlock_condition: { type: 'badge', value: 'morning_mist' },
    rewards: { xp: 500, coins: 1000 },
    merchants_count: 3,
    special_items: ['神秘物品', '时光碎片']
  },
  'herb_garden': {
    id: 'herb_garden',
    name: '草本花园',
    description: '芙萝拉的花园，各种草本植物的家园',
    coordinates: [39.9242, 116.4274],
    type: 'garden',
    unlocked: true,
    unlock_condition: null,
    rewards: { xp: 50, coins: 100 },
    merchants_count: 8,
    special_items: ['芜荽', '鼠尾草', '迷迭香', '百里香']
  },
  'hunters_lodge': {
    id: 'hunters_lodge',
    name: '猎人小屋',
    description: '凯恩的据点，冒险者的聚集地',
    coordinates: [39.8842, 116.4374],
    type: 'lodge',
    unlocked: false,
    unlock_condition: { type: 'tasks_completed', value: 5, category: 'hunting' },
    rewards: { xp: 150, coins: 300 },
    merchants_count: 2,
    special_items: ['狩猎装备', '野味']
  }
}

// 获取地图位置列表
router.get('/locations', optionalAuth, asyncHandler(async (req, res) => {
  const locations = Object.values(MAP_LOCATIONS)
  
  // 如果用户已登录，检查解锁状态
  if (req.user) {
    const { data: userProfile } = await supabase
      .from('users')
      .select('level, badges')
      .eq('id', req.user.id)
      .single()
    
    const { data: userExplorations } = await supabase
      .from('user_explorations')
      .select('location_id')
      .eq('user_id', req.user.id)
    
    const unlockedLocationIds = new Set(userExplorations?.map(e => e.location_id) || [])
    
    // 检查每个位置的解锁状态
    locations.forEach(location => {
      if (location.unlocked || unlockedLocationIds.has(location.id)) {
        location.user_unlocked = true
      } else {
        location.user_unlocked = false
        
        // 检查是否满足解锁条件
        if (location.unlock_condition) {
          const condition = location.unlock_condition
          let canUnlock = false
          
          switch (condition.type) {
            case 'level':
              canUnlock = userProfile?.level >= condition.value
              break
            case 'badge':
              canUnlock = userProfile?.badges?.some(badge => badge.id === condition.value)
              break
            case 'tasks_completed':
              // TODO: 实现任务完成数检查
              canUnlock = false
              break
          }
          
          location.can_unlock = canUnlock
        }
      }
    })
  } else {
    // 未登录用户只能看到默认解锁的位置
    locations.forEach(location => {
      location.user_unlocked = location.unlocked
      location.can_unlock = false
    })
  }
  
  res.json({
    success: true,
    data: locations
  })
}))

// 获取特定位置详情
router.get('/locations/:locationId', optionalAuth, asyncHandler(async (req, res) => {
  const { locationId } = req.params
  
  const location = MAP_LOCATIONS[locationId]
  if (!location) {
    return res.status(404).json({
      success: false,
      error: '位置不存在'
    })
  }
  
  let locationData = { ...location }
  
  // 如果用户已登录，检查解锁状态
  if (req.user) {
    const { data: exploration } = await supabase
      .from('user_explorations')
      .select('*')
      .eq('user_id', req.user.id)
      .eq('location_id', locationId)
      .single()
    
    locationData.user_unlocked = !!exploration || location.unlocked
    locationData.unlocked_at = exploration?.unlocked_at
  } else {
    locationData.user_unlocked = location.unlocked
  }
  
  // 获取该位置的商户信息
  if (locationData.user_unlocked) {
    const { data: merchants } = await supabase
      .from('merchants')
      .select(`
        id,
        name,
        description,
        rating,
        products (
          id,
          name,
          price,
          category
        )
      `)
      .ilike('location', `%${location.name}%`)
      .eq('status', 'active')
      .limit(10)
    
    locationData.merchants = merchants || []
  }
  
  res.json({
    success: true,
    data: locationData
  })
}))

// 解锁位置
router.post('/locations/:locationId/unlock', authMiddleware, asyncHandler(async (req, res) => {
  const { locationId } = req.params
  
  const location = MAP_LOCATIONS[locationId]
  if (!location) {
    return res.status(404).json({
      success: false,
      error: '位置不存在'
    })
  }
  
  // 检查是否已解锁
  const { data: existingExploration } = await supabase
    .from('user_explorations')
    .select('id')
    .eq('user_id', req.user.id)
    .eq('location_id', locationId)
    .single()
  
  if (existingExploration || location.unlocked) {
    return res.status(400).json({
      success: false,
      error: '位置已解锁'
    })
  }
  
  // 检查解锁条件
  if (location.unlock_condition) {
    const condition = location.unlock_condition
    const { data: userProfile } = await supabase
      .from('users')
      .select('level, badges')
      .eq('id', req.user.id)
      .single()
    
    let canUnlock = false
    let errorMessage = '不满足解锁条件'
    
    switch (condition.type) {
      case 'level':
        canUnlock = userProfile?.level >= condition.value
        if (!canUnlock) {
          errorMessage = `需要达到 ${condition.value} 级`
        }
        break
      case 'badge':
        canUnlock = userProfile?.badges?.some(badge => badge.id === condition.value)
        if (!canUnlock) {
          errorMessage = `需要获得 ${condition.value} 徽章`
        }
        break
      case 'tasks_completed':
        // TODO: 实现任务完成数检查
        const { count: completedTasks } = await supabase
          .from('tasks')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', req.user.id)
          .eq('status', 'completed')
          .eq('type', condition.category || 'any')
        
        canUnlock = completedTasks >= condition.value
        if (!canUnlock) {
          errorMessage = `需要完成 ${condition.value} 个${condition.category || ''}任务`
        }
        break
    }
    
    if (!canUnlock) {
      return res.status(400).json({
        success: false,
        error: errorMessage
      })
    }
  }
  
  // 解锁位置
  const { data: exploration, error } = await supabase
    .from('user_explorations')
    .insert({
      user_id: req.user.id,
      location_id: locationId,
      location_name: location.name
    })
    .select()
    .single()
  
  if (error) throw error
  
  // 给予奖励
  if (location.rewards.xp > 0) {
    await supabase.rpc('add_user_xp', {
      user_id: req.user.id,
      xp_amount: location.rewards.xp
    })
  }
  
  if (location.rewards.coins > 0) {
    await supabase.rpc('add_vanilla_coins', {
      user_id: req.user.id,
      coin_amount: location.rewards.coins
    })
  }
  
  // 发送解锁通知
  await supabase
    .from('messages')
    .insert({
      sender_id: req.user.id,
      recipient_id: req.user.id,
      title: '新位置解锁！',
      content: `恭喜解锁新位置：${location.name}！获得 ${location.rewards.xp} 经验值和 ${location.rewards.coins} 香草币。`,
      type: 'system'
    })
  
  res.status(201).json({
    success: true,
    data: {
      exploration,
      location: { ...location, user_unlocked: true },
      rewards: location.rewards
    },
    message: `成功解锁 ${location.name}！`
  })
}))

// 获取用户探索统计
router.get('/exploration/stats', authMiddleware, asyncHandler(async (req, res) => {
  const { data: explorations } = await supabase
    .from('user_explorations')
    .select('*')
    .eq('user_id', req.user.id)
    .order('unlocked_at', { ascending: false })
  
  const totalLocations = Object.keys(MAP_LOCATIONS).length
  const unlockedCount = explorations?.length || 0
  const explorationProgress = Math.round((unlockedCount / totalLocations) * 100)
  
  // 获取最近解锁的位置
  const recentUnlocks = explorations?.slice(0, 5) || []
  
  res.json({
    success: true,
    data: {
      totalLocations,
      unlockedCount,
      explorationProgress,
      recentUnlocks,
      allExplorations: explorations || []
    }
  })
}))

// 探索位置 (获得随机奖励)
router.post('/locations/:locationId/explore', authMiddleware, asyncHandler(async (req, res) => {
  const { locationId } = req.params
  
  const location = MAP_LOCATIONS[locationId]
  if (!location) {
    return res.status(404).json({
      success: false,
      error: '位置不存在'
    })
  }
  
  // 检查是否已解锁
  const { data: exploration } = await supabase
    .from('user_explorations')
    .select('id')
    .eq('user_id', req.user.id)
    .eq('location_id', locationId)
    .single()
  
  if (!exploration && !location.unlocked) {
    return res.status(400).json({
      success: false,
      error: '位置未解锁'
    })
  }
  
  // 生成随机奖励
  const randomRewards = {
    xp: Math.floor(Math.random() * 50) + 10, // 10-60 经验值
    coins: Math.floor(Math.random() * 30) + 5, // 5-35 香草币
  }
  
  // 特殊位置有机会获得特殊物品
  let specialReward = null
  if (location.type === 'forgotten' && Math.random() < 0.1) { // 10% 概率
    specialReward = {
      type: 'item',
      name: '时光碎片',
      description: '来自遗忘之地的神秘物品'
    }
  }
  
  // 给予奖励
  await supabase.rpc('add_user_xp', {
    user_id: req.user.id,
    xp_amount: randomRewards.xp
  })
  
  await supabase.rpc('add_vanilla_coins', {
    user_id: req.user.id,
    coin_amount: randomRewards.coins
  })
  
  // 记录探索活动 (可以用于统计和限制)
  // TODO: 可以添加探索冷却时间等机制
  
  res.json({
    success: true,
    data: {
      location: location.name,
      rewards: randomRewards,
      specialReward
    },
    message: `探索 ${location.name} 完成！获得 ${randomRewards.xp} 经验值和 ${randomRewards.coins} 香草币。${specialReward ? `还发现了特殊物品：${specialReward.name}！` : ''}`
  })
}))

export default router
