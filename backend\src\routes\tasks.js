import express from 'express'
import { authMiddleware } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { supabase } from '../services/supabase.js'

const router = express.Router()

// 获取用户任务列表
router.get('/', authMiddleware, asyncHandler(async (req, res) => {
  const { status = 'active', type, limit = 20, offset = 0 } = req.query
  
  let query = supabase
    .from('tasks')
    .select('*')
    .eq('user_id', req.user.id)
    .eq('status', status)
    .order('created_at', { ascending: false })
  
  if (type) {
    query = query.eq('type', type)
  }
  
  query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)
  
  const { data, error, count } = await query
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: count
    }
  })
}))

// 获取任务详情
router.get('/:id', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const { data: task, error } = await supabase
    .from('tasks')
    .select('*')
    .eq('id', id)
    .eq('user_id', req.user.id)
    .single()
  
  if (error) {
    return res.status(404).json({
      success: false,
      error: '任务不存在'
    })
  }
  
  res.json({
    success: true,
    data: task
  })
}))

// 完成任务
router.post('/:id/complete', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const result = await supabase.rpc('complete_task', {
    task_id: id,
    user_id: req.user.id
  })
  
  if (result.error) throw result.error
  
  const completionResult = result.data[0]
  
  if (!completionResult.success) {
    return res.status(400).json({
      success: false,
      error: '任务无法完成或已完成'
    })
  }
  
  res.json({
    success: true,
    data: {
      rewards: completionResult.rewards
    },
    message: '任务完成！奖励已发放。'
  })
}))

// 更新任务进度
router.put('/:id/progress', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  const { progress } = req.body
  
  if (typeof progress !== 'number' || progress < 0) {
    return res.status(400).json({
      success: false,
      error: '进度值必须是非负数'
    })
  }
  
  // 获取任务信息
  const { data: task, error: taskError } = await supabase
    .from('tasks')
    .select('*')
    .eq('id', id)
    .eq('user_id', req.user.id)
    .eq('status', 'active')
    .single()
  
  if (taskError || !task) {
    return res.status(404).json({
      success: false,
      error: '任务不存在或已完成'
    })
  }
  
  const newProgress = Math.min(progress, task.max_progress)
  
  // 更新进度
  const { data: updatedTask, error } = await supabase
    .from('tasks')
    .update({ 
      progress: newProgress,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()
  
  if (error) throw error
  
  // 如果任务完成，自动触发完成逻辑
  if (newProgress >= task.max_progress) {
    const completionResult = await supabase.rpc('complete_task', {
      task_id: id,
      user_id: req.user.id
    })
    
    if (completionResult.data && completionResult.data[0]?.success) {
      return res.json({
        success: true,
        data: updatedTask,
        completed: true,
        rewards: completionResult.data[0].rewards,
        message: '任务进度更新成功！任务已完成，奖励已发放。'
      })
    }
  }
  
  res.json({
    success: true,
    data: updatedTask,
    message: '任务进度更新成功'
  })
}))

// 创建每日任务 (系统调用)
router.post('/daily/generate', asyncHandler(async (req, res) => {
  // 这个端点通常由定时任务调用，为所有用户生成每日任务
  const { user_ids } = req.body
  
  if (!Array.isArray(user_ids)) {
    return res.status(400).json({
      success: false,
      error: '用户ID列表格式错误'
    })
  }
  
  const dailyTasks = [
    {
      title: '每日签到',
      description: '登录斯卡布罗集市，开始新的一天',
      type: 'daily',
      organization: '集市管理委员会',
      rewards: { xp: 20, coins: 10 },
      conditions: { type: 'login', count: 1 },
      max_progress: 1
    },
    {
      title: '浏览商品',
      description: '浏览5件不同的商品，发现有趣的物品',
      type: 'daily',
      organization: '商户联盟',
      rewards: { xp: 30, coins: 15 },
      conditions: { type: 'view_products', count: 5 },
      max_progress: 5
    },
    {
      title: '社交互动',
      description: '为其他用户的日记点赞3次',
      type: 'daily',
      organization: '文学爱好者协会',
      rewards: { xp: 25, coins: 20 },
      conditions: { type: 'like_diary', count: 3 },
      max_progress: 3
    }
  ]
  
  const tasksToInsert = []
  
  for (const userId of user_ids) {
    // 检查用户今天是否已有每日任务
    const today = new Date().toISOString().split('T')[0]
    const { data: existingTasks } = await supabase
      .from('tasks')
      .select('id')
      .eq('user_id', userId)
      .eq('type', 'daily')
      .gte('created_at', `${today}T00:00:00.000Z`)
      .lt('created_at', `${today}T23:59:59.999Z`)
    
    if (existingTasks && existingTasks.length > 0) {
      continue // 用户今天已有每日任务
    }
    
    // 为用户随机选择2-3个每日任务
    const selectedTasks = dailyTasks
      .sort(() => 0.5 - Math.random())
      .slice(0, Math.floor(Math.random() * 2) + 2)
    
    for (const task of selectedTasks) {
      tasksToInsert.push({
        ...task,
        user_id: userId,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24小时后过期
      })
    }
  }
  
  if (tasksToInsert.length > 0) {
    const { data, error } = await supabase
      .from('tasks')
      .insert(tasksToInsert)
      .select()
    
    if (error) throw error
    
    res.json({
      success: true,
      data: {
        generated_tasks: data.length,
        tasks: data
      },
      message: `成功为 ${user_ids.length} 个用户生成 ${data.length} 个每日任务`
    })
  } else {
    res.json({
      success: true,
      data: {
        generated_tasks: 0,
        tasks: []
      },
      message: '所有用户今天都已有每日任务'
    })
  }
}))

// 创建特殊任务 (管理员功能)
router.post('/special', authMiddleware, asyncHandler(async (req, res) => {
  const { 
    title, 
    description, 
    target_users, 
    rewards, 
    conditions, 
    max_progress,
    expires_in_hours = 168 // 默认7天
  } = req.body
  
  // 简单的权限检查 (实际应用中需要更严格的管理员验证)
  const { data: userProfile } = await supabase
    .from('users')
    .select('role')
    .eq('id', req.user.id)
    .single()
  
  if (userProfile?.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: '权限不足'
    })
  }
  
  const tasksToInsert = target_users.map(userId => ({
    user_id: userId,
    title,
    description,
    type: 'special',
    organization: '集市管理委员会',
    rewards,
    conditions,
    max_progress,
    expires_at: new Date(Date.now() + expires_in_hours * 60 * 60 * 1000).toISOString()
  }))
  
  const { data, error } = await supabase
    .from('tasks')
    .insert(tasksToInsert)
    .select()
  
  if (error) throw error
  
  // 发送任务通知
  const notifications = target_users.map(userId => ({
    sender_id: req.user.id,
    recipient_id: userId,
    title: '新的特殊任务',
    content: `您收到了一个新的特殊任务：${title}`,
    type: 'system'
  }))
  
  await supabase
    .from('messages')
    .insert(notifications)
  
  res.status(201).json({
    success: true,
    data,
    message: `成功创建 ${data.length} 个特殊任务`
  })
}))

// 获取任务统计
router.get('/stats/summary', authMiddleware, asyncHandler(async (req, res) => {
  const userId = req.user.id
  
  const [
    { count: activeTasks },
    { count: completedTasks },
    { count: expiredTasks }
  ] = await Promise.all([
    supabase.from('tasks').select('*', { count: 'exact', head: true }).eq('user_id', userId).eq('status', 'active'),
    supabase.from('tasks').select('*', { count: 'exact', head: true }).eq('user_id', userId).eq('status', 'completed'),
    supabase.from('tasks').select('*', { count: 'exact', head: true }).eq('user_id', userId).eq('status', 'expired')
  ])
  
  // 获取最近完成的任务
  const { data: recentCompleted } = await supabase
    .from('tasks')
    .select('title, completed_at, rewards')
    .eq('user_id', userId)
    .eq('status', 'completed')
    .order('completed_at', { ascending: false })
    .limit(5)
  
  res.json({
    success: true,
    data: {
      activeTasks,
      completedTasks,
      expiredTasks,
      totalTasks: activeTasks + completedTasks + expiredTasks,
      recentCompleted
    }
  })
}))

export default router
