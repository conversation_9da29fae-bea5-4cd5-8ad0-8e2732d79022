import express from 'express'
import { authMiddleware, requireMerchantAccess } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { supabase } from '../services/supabase.js'

const router = express.Router()

// 获取所有活跃商户
router.get('/', asyncHandler(async (req, res) => {
  const { location, limit = 20, offset = 0 } = req.query
  
  let query = supabase
    .from('merchants')
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .eq('status', 'active')
    .order('created_at', { ascending: false })
  
  if (location) {
    query = query.ilike('location', `%${location}%`)
  }
  
  query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)
  
  const { data, error, count } = await query
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: count
    }
  })
}))

// 获取商户详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const { data: merchant, error } = await supabase
    .from('merchants')
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      ),
      products (
        id,
        name,
        price,
        category,
        status,
        stock_quantity
      )
    `)
    .eq('id', id)
    .single()
  
  if (error) {
    return res.status(404).json({
      success: false,
      error: '商户不存在'
    })
  }
  
  res.json({
    success: true,
    data: merchant
  })
}))

// 创建商户 (需要认证)
router.post('/', authMiddleware, asyncHandler(async (req, res) => {
  const { name, description, location, contact_info, business_hours } = req.body
  
  // 检查用户是否已有商户
  const { data: existingMerchant } = await supabase
    .from('merchants')
    .select('id')
    .eq('user_id', req.user.id)
    .single()
  
  if (existingMerchant) {
    return res.status(400).json({
      success: false,
      error: '用户已有商户，每个用户只能创建一个商户'
    })
  }
  
  const { data: merchant, error } = await supabase
    .from('merchants')
    .insert({
      user_id: req.user.id,
      name,
      description,
      location,
      contact_info,
      business_hours
    })
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .single()
  
  if (error) throw error
  
  // 给用户增加经验值
  await supabase.rpc('add_user_xp', {
    user_id: req.user.id,
    xp_amount: 100
  })
  
  res.status(201).json({
    success: true,
    data: merchant,
    message: '商户创建成功！获得100经验值奖励。'
  })
}))

// 更新商户信息 (需要商户权限)
router.put('/:merchantId', authMiddleware, requireMerchantAccess, asyncHandler(async (req, res) => {
  const { merchantId } = req.params
  const { name, description, location, contact_info, business_hours } = req.body
  
  const { data: merchant, error } = await supabase
    .from('merchants')
    .update({
      name,
      description,
      location,
      contact_info,
      business_hours,
      updated_at: new Date().toISOString()
    })
    .eq('id', merchantId)
    .select(`
      *,
      users (
        username,
        level,
        avatar_url
      )
    `)
    .single()
  
  if (error) throw error
  
  res.json({
    success: true,
    data: merchant,
    message: '商户信息更新成功'
  })
}))

// 获取商户的商品列表
router.get('/:id/products', asyncHandler(async (req, res) => {
  const { id } = req.params
  const { category, status = 'active', limit = 20, offset = 0 } = req.query
  
  let query = supabase
    .from('products')
    .select('*')
    .eq('merchant_id', id)
    .eq('status', status)
    .order('created_at', { ascending: false })
  
  if (category) {
    query = query.eq('category', category)
  }
  
  query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)
  
  const { data, error, count } = await query
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: count
    }
  })
}))

// 添加商品 (需要商户权限)
router.post('/:merchantId/products', authMiddleware, requireMerchantAccess, asyncHandler(async (req, res) => {
  const { merchantId } = req.params
  const { 
    name, 
    description, 
    price, 
    category, 
    images, 
    availability_conditions, 
    stock_quantity, 
    tags 
  } = req.body
  
  const { data: product, error } = await supabase
    .from('products')
    .insert({
      merchant_id: merchantId,
      name,
      description,
      price,
      category,
      images,
      availability_conditions,
      stock_quantity,
      tags
    })
    .select()
    .single()
  
  if (error) throw error
  
  // 给商户增加经验值
  await supabase.rpc('add_user_xp', {
    user_id: req.user.id,
    xp_amount: 20
  })
  
  res.status(201).json({
    success: true,
    data: product,
    message: '商品添加成功！获得20经验值奖励。'
  })
}))

// 更新商品 (需要商户权限)
router.put('/:merchantId/products/:productId', authMiddleware, requireMerchantAccess, asyncHandler(async (req, res) => {
  const { productId } = req.params
  const { 
    name, 
    description, 
    price, 
    category, 
    images, 
    availability_conditions, 
    stock_quantity, 
    tags,
    status
  } = req.body
  
  const { data: product, error } = await supabase
    .from('products')
    .update({
      name,
      description,
      price,
      category,
      images,
      availability_conditions,
      stock_quantity,
      tags,
      status,
      updated_at: new Date().toISOString()
    })
    .eq('id', productId)
    .select()
    .single()
  
  if (error) throw error
  
  res.json({
    success: true,
    data: product,
    message: '商品更新成功'
  })
}))

// 删除商品 (需要商户权限)
router.delete('/:merchantId/products/:productId', authMiddleware, requireMerchantAccess, asyncHandler(async (req, res) => {
  const { productId } = req.params
  
  const { error } = await supabase
    .from('products')
    .delete()
    .eq('id', productId)
  
  if (error) throw error
  
  res.json({
    success: true,
    message: '商品删除成功'
  })
}))

// 获取商户统计信息
router.get('/:id/stats', asyncHandler(async (req, res) => {
  const { id } = req.params
  
  // 并行查询各种统计数据
  const [
    { count: totalProducts },
    { count: totalOrders },
    { data: recentOrders }
  ] = await Promise.all([
    supabase.from('products').select('*', { count: 'exact', head: true }).eq('merchant_id', id),
    supabase.from('orders').select('*', { count: 'exact', head: true }).eq('merchant_id', id),
    supabase.from('orders')
      .select(`
        *,
        products (name),
        users (username)
      `)
      .eq('merchant_id', id)
      .order('created_at', { ascending: false })
      .limit(5)
  ])
  
  // 计算总销售额
  const { data: salesData } = await supabase
    .from('orders')
    .select('total_price')
    .eq('merchant_id', id)
    .eq('payment_status', 'completed')
  
  const totalRevenue = salesData?.reduce((sum, order) => sum + parseFloat(order.total_price), 0) || 0
  
  res.json({
    success: true,
    data: {
      totalProducts,
      totalOrders,
      totalRevenue: totalRevenue.toFixed(2),
      recentOrders
    }
  })
}))

export default router
