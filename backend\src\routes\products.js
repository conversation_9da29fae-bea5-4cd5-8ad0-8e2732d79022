import express from 'express'
import { authMiddleware, optionalAuth } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { dbHelpers, supabase } from '../services/supabase.js'

const router = express.Router()

// 获取推荐商品
router.get('/recommendations', optionalAuth, asyncHandler(async (req, res) => {
  const { weather, location, category, limit = 20 } = req.query
  
  const filters = {}
  if (weather) filters.weather = weather
  if (location) filters.location = location
  if (category) filters.category = category
  
  const products = await dbHelpers.getProducts(filters)
  
  // 如果用户已登录，可以基于用户偏好进行个性化推荐
  if (req.user) {
    // TODO: 实现协同过滤算法
    // 这里可以根据用户的购买历史、浏览记录等进行个性化推荐
  }
  
  res.json({
    success: true,
    data: products.slice(0, parseInt(limit)),
    filters: {
      weather,
      location,
      category,
      applied: Object.keys(filters).length > 0
    }
  })
}))

// 搜索商品
router.get('/search', optionalAuth, asyncHandler(async (req, res) => {
  const { 
    q: searchTerm, 
    category, 
    minPrice, 
    maxPrice, 
    location,
    sortBy = 'created_at',
    order = 'desc',
    page = 1,
    limit = 20
  } = req.query
  
  if (!searchTerm) {
    return res.status(400).json({
      success: false,
      error: '搜索关键词不能为空'
    })
  }
  
  let query = supabase
    .from('products')
    .select(`
      *,
      merchants (
        id,
        name,
        location,
        users (
          username,
          level
        )
      )
    `)
    .eq('status', 'active')
    .ilike('name', `%${searchTerm}%`)
  
  // 应用过滤器
  if (category) {
    query = query.eq('category', category)
  }
  
  if (minPrice) {
    query = query.gte('price', parseFloat(minPrice))
  }
  
  if (maxPrice) {
    query = query.lte('price', parseFloat(maxPrice))
  }
  
  if (location) {
    query = query.eq('merchants.location', location)
  }
  
  // 排序
  const ascending = order === 'asc'
  query = query.order(sortBy, { ascending })
  
  // 分页
  const offset = (parseInt(page) - 1) * parseInt(limit)
  query = query.range(offset, offset + parseInt(limit) - 1)
  
  const { data, error, count } = await query
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      totalPages: Math.ceil(count / parseInt(limit))
    },
    searchTerm,
    filters: {
      category,
      priceRange: minPrice || maxPrice ? { min: minPrice, max: maxPrice } : null,
      location
    }
  })
}))

// 获取商品详情
router.get('/:id', optionalAuth, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const { data: product, error } = await supabase
    .from('products')
    .select(`
      *,
      merchants (
        id,
        name,
        location,
        description,
        users (
          username,
          level,
          avatar_url
        )
      )
    `)
    .eq('id', id)
    .single()
  
  if (error) {
    return res.status(404).json({
      success: false,
      error: '商品不存在'
    })
  }
  
  // 如果用户已登录，记录浏览历史
  if (req.user) {
    // TODO: 记录用户浏览历史，用于推荐算法
    await supabase
      .from('user_product_views')
      .upsert({
        user_id: req.user.id,
        product_id: id,
        viewed_at: new Date().toISOString()
      })
      .select()
  }
  
  res.json({
    success: true,
    data: product
  })
}))

// 获取商品分类
router.get('/categories/list', asyncHandler(async (req, res) => {
  const { data, error } = await supabase
    .from('products')
    .select('category')
    .eq('status', 'active')
  
  if (error) throw error
  
  // 统计每个分类的商品数量
  const categoryCount = data.reduce((acc, item) => {
    acc[item.category] = (acc[item.category] || 0) + 1
    return acc
  }, {})
  
  const categories = Object.entries(categoryCount).map(([name, count]) => ({
    name,
    count
  }))
  
  res.json({
    success: true,
    data: categories
  })
}))

// 获取热门商品
router.get('/trending/list', optionalAuth, asyncHandler(async (req, res) => {
  const { limit = 10, timeframe = '7d' } = req.query
  
  // 计算时间范围
  const timeframeDays = timeframe === '1d' ? 1 : timeframe === '7d' ? 7 : 30
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - timeframeDays)
  
  // 基于订单数量和浏览量计算热门商品
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      merchants (
        id,
        name,
        location,
        users (
          username
        )
      ),
      orders!inner (
        created_at
      )
    `)
    .eq('status', 'active')
    .gte('orders.created_at', startDate.toISOString())
    .limit(parseInt(limit))
  
  if (error) throw error
  
  res.json({
    success: true,
    data,
    timeframe,
    message: `过去${timeframeDays}天的热门商品`
  })
}))

// 获取相似商品
router.get('/:id/similar', optionalAuth, asyncHandler(async (req, res) => {
  const { id } = req.params
  const { limit = 5 } = req.query
  
  // 首先获取当前商品信息
  const { data: currentProduct, error: currentError } = await supabase
    .from('products')
    .select('category, price, merchants(location)')
    .eq('id', id)
    .single()
  
  if (currentError) {
    return res.status(404).json({
      success: false,
      error: '商品不存在'
    })
  }
  
  // 查找相似商品（同分类、相似价格范围、相同地区）
  const priceRange = currentProduct.price * 0.3 // 30% 价格浮动范围
  
  const { data: similarProducts, error } = await supabase
    .from('products')
    .select(`
      *,
      merchants (
        id,
        name,
        location,
        users (
          username
        )
      )
    `)
    .eq('status', 'active')
    .eq('category', currentProduct.category)
    .neq('id', id)
    .gte('price', currentProduct.price - priceRange)
    .lte('price', currentProduct.price + priceRange)
    .limit(parseInt(limit))
  
  if (error) throw error
  
  res.json({
    success: true,
    data: similarProducts
  })
}))

export default router
