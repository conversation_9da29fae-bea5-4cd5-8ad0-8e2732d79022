import React, { useState, useEffect } from 'react'
import { 
  Book, 
  Star, 
  Lock, 
  Play, 
  CheckCircle, 
  Eye,
  EyeOff,
  Scroll,
  Crown,
  Sparkles
} from 'lucide-react'

const StoryPage = ({ user }) => {
  const [stories, setStories] = useState([])
  const [currentStory, setCurrentStory] = useState(null)
  const [showHidden, setShowHidden] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadStories()
    }
  }, [user])

  const loadStories = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/story/progress', {
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })
      
      const result = await response.json()
      if (result.success) {
        setStories(result.data)
      }
    } catch (error) {
      console.error('加载剧情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const startChapter = async (storyId, chapterId) => {
    try {
      const response = await fetch(`/api/story/${storyId}/chapters/${chapterId}/start`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })
      
      const result = await response.json()
      if (result.success) {
        const story = stories.find(s => s.story_id === storyId)
        const chapter = story?.chapters?.find(c => c.id === chapterId)
        setCurrentStory({ ...story, currentChapter: chapter })
      }
    } catch (error) {
      console.error('开始章节失败:', error)
    }
  }

  const completeChapter = async (storyId, chapterId) => {
    try {
      const response = await fetch(`/api/story/${storyId}/chapters/${chapterId}/complete`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })
      
      const result = await response.json()
      if (result.success) {
        alert(`章节完成！获得奖励：${JSON.stringify(result.rewards)}`)
        loadStories() // 重新加载进度
        setCurrentStory(null)
      }
    } catch (error) {
      console.error('完成章节失败:', error)
    }
  }

  const getStoryTypeIcon = (type) => {
    switch (type) {
      case 'main':
        return <Book className="text-herb-green" size={20} />
      case 'hidden':
        return <Eye className="text-rosemary-purple" size={20} />
      case 'npc':
        return <Star className="text-herb-gold" size={20} />
      default:
        return <Scroll className="text-sage-gray" size={20} />
    }
  }

  const getStoryTypeLabel = (type) => {
    switch (type) {
      case 'main':
        return '主线剧情'
      case 'hidden':
        return '隐藏剧情'
      case 'npc':
        return 'NPC故事'
      default:
        return '支线剧情'
    }
  }

  // 模拟剧情数据
  const mockStories = [
    {
      story_id: 'scarborough_legacy',
      title: '斯卡布罗集市的传承',
      type: 'main',
      description: '探索古老集市的秘密，成为真正的传承者',
      progress: 60,
      chapters: [
        {
          id: 'arrival',
          title: '初来乍到',
          completed: true,
          unlocked: true
        },
        {
          id: 'first_trade',
          title: '初次交易',
          completed: true,
          unlocked: true
        },
        {
          id: 'merchant_path',
          title: '商户之路',
          completed: false,
          unlocked: true
        },
        {
          id: 'community_bonds',
          title: '社区纽带',
          completed: false,
          unlocked: false
        }
      ]
    },
    {
      story_id: 'morning_mist_walker',
      title: '晨雾行者的传说',
      type: 'hidden',
      description: '追寻神秘的晨雾行者足迹，解开古老的谜团',
      progress: 33,
      unlocked: user?.badges?.some(b => b.id === 'morning_mist'),
      chapters: [
        {
          id: 'first_glimpse',
          title: '初见晨雾',
          completed: true,
          unlocked: true
        },
        {
          id: 'following_traces',
          title: '追寻足迹',
          completed: false,
          unlocked: true
        },
        {
          id: 'the_revelation',
          title: '真相大白',
          completed: false,
          unlocked: false
        }
      ]
    }
  ]

  const displayStories = stories.length > 0 ? stories : mockStories
  const filteredStories = showHidden 
    ? displayStories 
    : displayStories.filter(story => story.type !== 'hidden' || story.unlocked)

  if (!user) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📖</div>
        <h2 className="text-2xl font-medieval text-herb-green mb-4">剧情中心</h2>
        <p className="text-sage-gray mb-6">登录后开始您的传奇故事</p>
        <button className="btn-medieval">立即登录</button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-medieval text-herb-green mb-2">剧情中心</h1>
        <p className="text-sage-gray">在斯卡布罗集市中书写属于您的传奇故事</p>
      </div>

      {/* Story Progress Overview */}
      <div className="card-medieval bg-gradient-to-r from-herb-green/10 to-rosemary-purple/10">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-medieval text-herb-green">故事进度</h3>
          <button
            onClick={() => setShowHidden(!showHidden)}
            className="flex items-center space-x-2 text-sage-gray hover:text-herb-green"
          >
            {showHidden ? <EyeOff size={16} /> : <Eye size={16} />}
            <span className="text-sm">{showHidden ? '隐藏秘密' : '显示秘密'}</span>
          </button>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-herb-green">
              {displayStories.filter(s => s.type === 'main').length}
            </div>
            <div className="text-sm text-sage-gray">主线剧情</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-rosemary-purple">
              {displayStories.filter(s => s.type === 'hidden' && s.unlocked).length}
            </div>
            <div className="text-sm text-sage-gray">隐藏剧情</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-herb-gold">
              {displayStories.reduce((sum, s) => sum + (s.chapters?.filter(c => c.completed).length || 0), 0)}
            </div>
            <div className="text-sm text-sage-gray">完成章节</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-sage-gray">
              {Math.round(displayStories.reduce((sum, s) => sum + (s.progress || 0), 0) / displayStories.length) || 0}%
            </div>
            <div className="text-sm text-sage-gray">总体进度</div>
          </div>
        </div>
      </div>

      {/* Story List */}
      <div className="space-y-6">
        {filteredStories.map((story) => (
          <div
            key={story.story_id}
            className={`card-medieval ${
              story.type === 'hidden' 
                ? 'border-rosemary-purple/50 bg-gradient-to-r from-rosemary-purple/5 to-herb-green/5' 
                : ''
            }`}
          >
            {/* Story Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                {getStoryTypeIcon(story.type)}
                <div>
                  <h3 className="text-xl font-medieval text-herb-green flex items-center space-x-2">
                    <span>{story.title}</span>
                    {story.type === 'hidden' && <Sparkles size={16} className="text-rosemary-purple" />}
                  </h3>
                  <p className="text-sm text-sage-gray">{getStoryTypeLabel(story.type)}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-lg font-bold text-herb-green">{story.progress || 0}%</div>
                <div className="text-xs text-sage-gray">完成度</div>
              </div>
            </div>

            {/* Story Description */}
            <p className="text-sage-gray mb-4 leading-relaxed">{story.description}</p>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="progress-bar">
                <div 
                  className={`progress-fill ${
                    story.type === 'hidden' ? 'bg-gradient-to-r from-rosemary-purple to-herb-gold' : ''
                  }`}
                  style={{ width: `${story.progress || 0}%` }}
                ></div>
              </div>
            </div>

            {/* Chapters */}
            {story.chapters && (
              <div className="space-y-3">
                <h4 className="font-medium text-herb-green">章节进度</h4>
                <div className="grid md:grid-cols-2 gap-3">
                  {story.chapters.map((chapter, index) => (
                    <div
                      key={chapter.id}
                      className={`flex items-center justify-between p-3 rounded-lg border ${
                        chapter.completed
                          ? 'bg-green-50 border-green-200'
                          : chapter.unlocked
                            ? 'bg-herb-green/10 border-herb-green/30'
                            : 'bg-sage-gray/10 border-sage-gray/30'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                          chapter.completed
                            ? 'bg-green-500 text-white'
                            : chapter.unlocked
                              ? 'bg-herb-green text-white'
                              : 'bg-sage-gray text-white'
                        }`}>
                          {chapter.completed ? <CheckCircle size={16} /> : chapter.unlocked ? index + 1 : <Lock size={16} />}
                        </div>
                        <div>
                          <h5 className="font-medium text-herb-green">{chapter.title}</h5>
                          <p className="text-xs text-sage-gray">
                            {chapter.completed ? '已完成' : chapter.unlocked ? '可进行' : '未解锁'}
                          </p>
                        </div>
                      </div>
                      
                      {chapter.unlocked && !chapter.completed && (
                        <button
                          onClick={() => startChapter(story.story_id, chapter.id)}
                          className="flex items-center space-x-1 px-3 py-1 bg-herb-green text-white rounded-lg hover:bg-rosemary-purple transition-colors duration-200"
                        >
                          <Play size={14} />
                          <span className="text-xs">开始</span>
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Unlock Condition for Hidden Stories */}
            {story.type === 'hidden' && !story.unlocked && (
              <div className="mt-4 p-3 bg-rosemary-purple/10 rounded-lg border border-rosemary-purple/30">
                <div className="flex items-center space-x-2 text-rosemary-purple">
                  <Lock size={16} />
                  <span className="font-medium">解锁条件</span>
                </div>
                <p className="text-sm text-sage-gray mt-1">
                  创作一篇晨雾行者诗篇并达到15级
                </p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Story Reading Modal */}
      {currentStory && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-parchment rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-medieval text-herb-green">
                    {currentStory.currentChapter?.title}
                  </h2>
                  <p className="text-sage-gray">{currentStory.title}</p>
                </div>
                <button
                  onClick={() => setCurrentStory(null)}
                  className="text-sage-gray hover:text-herb-green"
                >
                  ✕
                </button>
              </div>

              {/* Story Content */}
              <div className="prose prose-lg max-w-none text-sage-gray mb-8 leading-relaxed whitespace-pre-line">
                {currentStory.currentChapter?.story_text || `
                  微风轻抚过古老的石板路，你踏进了传说中的斯卡布罗集市。
                  空气中弥漫着芜荽、鼠尾草、迷迭香和百里香的香气，
                  仿佛那首古老的歌谣在耳边轻柔地响起。
                  
                  一位身着华丽服装的游吟诗人注意到了你...
                  
                  "欢迎来到斯卡布罗集市，年轻的旅人，"他微笑着说道，
                  "这里的每一个故事都等待着被发现，每一次相遇都可能改变命运。"
                `}
              </div>

              {/* Chapter Actions */}
              <div className="flex justify-between">
                <button
                  onClick={() => setCurrentStory(null)}
                  className="px-6 py-2 border border-sage-gray rounded-lg hover:bg-sage-gray/20"
                >
                  稍后阅读
                </button>
                <button
                  onClick={() => completeChapter(currentStory.story_id, currentStory.currentChapter.id)}
                  className="btn-medieval"
                >
                  完成章节
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Easter Eggs Hint */}
      <div className="card-medieval bg-gradient-to-r from-herb-gold/10 to-rosemary-purple/10">
        <h3 className="text-lg font-medieval text-herb-green mb-3">💡 彩蛋提示</h3>
        <div className="space-y-2 text-sm text-sage-gray">
          <p>• 在特定时间（如黎明或深夜）访问某些地点可能触发隐藏剧情</p>
          <p>• 与NPC建立深厚友谊后，他们可能会分享个人故事</p>
          <p>• 收集特定的草本组合可能解锁古老的秘密</p>
          <p>• 在满月之夜探索遗忘之地，可能会有意想不到的发现</p>
        </div>
      </div>
    </div>
  )
}

export default StoryPage
