import React, { useState, useEffect } from 'react'
import { 
  Target, 
  Calendar, 
  TrendingUp, 
  Star, 
  Award,
  Filter,
  RefreshCw
} from 'lucide-react'
import TaskCard from '../components/TaskCard'
import { supabase } from '../services/supabase'

const TasksPage = ({ user }) => {
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('active')
  const [typeFilter, setTypeFilter] = useState('')
  const [stats, setStats] = useState(null)

  useEffect(() => {
    if (user) {
      loadTasks()
      loadStats()
    }
  }, [user, filter, typeFilter])

  const loadTasks = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        status: filter,
        limit: '50'
      })
      
      if (typeFilter) {
        params.append('type', typeFilter)
      }
      
      const response = await fetch(`/api/tasks?${params}`, {
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })
      
      const result = await response.json()
      if (result.success) {
        setTasks(result.data)
      }
    } catch (error) {
      console.error('加载任务失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await fetch('/api/tasks/stats/summary', {
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })
      
      const result = await response.json()
      if (result.success) {
        setStats(result.data)
      }
    } catch (error) {
      console.error('加载统计失败:', error)
    }
  }

  const handleCompleteTask = async (taskId) => {
    try {
      const response = await fetch(`/api/tasks/${taskId}/complete`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        }
      })
      
      const result = await response.json()
      if (result.success) {
        // 刷新任务列表
        loadTasks()
        loadStats()
        
        // 显示奖励通知
        alert(result.message)
      } else {
        alert(result.error || '完成任务失败')
      }
    } catch (error) {
      console.error('完成任务失败:', error)
      alert('完成任务失败，请重试')
    }
  }

  const filterOptions = [
    { value: 'active', label: '进行中', icon: Target },
    { value: 'completed', label: '已完成', icon: Award },
    { value: 'expired', label: '已过期', icon: Calendar }
  ]

  const typeOptions = [
    { value: '', label: '全部类型' },
    { value: 'daily', label: '每日任务' },
    { value: 'weekly', label: '周任务' },
    { value: 'special', label: '特殊任务' },
    { value: 'exploration', label: '探索任务' }
  ]

  if (!user) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🎯</div>
        <h2 className="text-2xl font-medieval text-herb-green mb-4">任务中心</h2>
        <p className="text-sage-gray mb-6">登录后查看和完成各种任务</p>
        <button
          onClick={() => supabase.auth.signInWithOAuth({ provider: 'google' })}
          className="btn-medieval"
        >
          立即登录
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="text-center">
        <h1 className="text-3xl font-medieval text-herb-green mb-2">任务中心</h1>
        <p className="text-sage-gray">完成任务获得经验值、香草币和特殊奖励</p>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="card-medieval text-center">
            <div className="text-2xl font-bold text-herb-green">{stats.activeTasks}</div>
            <div className="text-sm text-sage-gray">进行中</div>
          </div>
          <div className="card-medieval text-center">
            <div className="text-2xl font-bold text-green-600">{stats.completedTasks}</div>
            <div className="text-sm text-sage-gray">已完成</div>
          </div>
          <div className="card-medieval text-center">
            <div className="text-2xl font-bold text-red-500">{stats.expiredTasks}</div>
            <div className="text-sm text-sage-gray">已过期</div>
          </div>
          <div className="card-medieval text-center">
            <div className="text-2xl font-bold text-rosemary-purple">{stats.totalTasks}</div>
            <div className="text-sm text-sage-gray">总任务</div>
          </div>
        </div>
      )}

      {/* 过滤器 */}
      <div className="card-medieval">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
          <div className="flex flex-wrap gap-2">
            {filterOptions.map(option => {
              const Icon = option.icon
              return (
                <button
                  key={option.value}
                  onClick={() => setFilter(option.value)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors duration-200 ${
                    filter === option.value
                      ? 'bg-herb-green text-parchment'
                      : 'bg-sage-gray/20 text-sage-gray hover:bg-herb-green/20'
                  }`}
                >
                  <Icon size={16} />
                  <span>{option.label}</span>
                </button>
              )
            })}
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 border border-sage-gray rounded-lg focus:outline-none focus:ring-2 focus:ring-herb-green"
            >
              {typeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            
            <button
              onClick={loadTasks}
              className="p-2 bg-herb-green text-parchment rounded-lg hover:bg-rosemary-purple transition-colors duration-200"
            >
              <RefreshCw size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* 最近完成的任务 */}
      {stats?.recentCompleted && stats.recentCompleted.length > 0 && filter === 'active' && (
        <div className="card-medieval">
          <h3 className="text-xl font-medieval text-herb-green mb-4">最近完成</h3>
          <div className="space-y-3">
            {stats.recentCompleted.map((task, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-green-700">{task.title}</h4>
                  <p className="text-sm text-green-600">
                    {new Date(task.completed_at).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  {task.rewards?.xp && (
                    <span className="flex items-center space-x-1 text-herb-green">
                      <Star size={14} />
                      <span>{task.rewards.xp}</span>
                    </span>
                  )}
                  {task.rewards?.coins && (
                    <span className="flex items-center space-x-1 text-herb-gold">
                      <span>🪙</span>
                      <span>{task.rewards.coins}</span>
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 任务列表 */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-herb-green mx-auto mb-4"></div>
          <p className="text-sage-gray">加载任务中...</p>
        </div>
      ) : tasks.length > 0 ? (
        <div className="grid md:grid-cols-2 gap-6">
          {tasks.map((task) => (
            <TaskCard
              key={task.id}
              task={task}
              user={user}
              onComplete={handleCompleteTask}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">
            {filter === 'active' ? '🎯' : filter === 'completed' ? '✅' : '⏰'}
          </div>
          <h3 className="text-xl font-medieval text-herb-green mb-2">
            {filter === 'active' && '暂无进行中的任务'}
            {filter === 'completed' && '还没有完成任务'}
            {filter === 'expired' && '没有过期的任务'}
          </h3>
          <p className="text-sage-gray">
            {filter === 'active' && '新的任务会定期发布，请稍后再来查看'}
            {filter === 'completed' && '完成任务来获得丰厚奖励吧'}
            {filter === 'expired' && '保持活跃，不要错过任务时间'}
          </p>
        </div>
      )}

      {/* 任务提示 */}
      <div className="card-medieval bg-gradient-to-r from-herb-green/10 to-rosemary-purple/10">
        <h3 className="text-lg font-medieval text-herb-green mb-3">💡 任务小贴士</h3>
        <div className="space-y-2 text-sm text-sage-gray">
          <p>• 每日任务会在每天重置，记得及时完成</p>
          <p>• 完成任务可获得经验值、香草币和特殊徽章</p>
          <p>• 特殊任务通常有更丰厚的奖励，但条件也更苛刻</p>
          <p>• 探索任务需要在地图上解锁新地点才能完成</p>
        </div>
      </div>
    </div>
  )
}

export default TasksPage
