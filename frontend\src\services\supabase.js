import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// 用户相关 API
export const userAPI = {
  // 获取用户档案
  async getProfile(userId) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error) throw error
    return data
  },

  // 更新用户档案
  async updateProfile(userId, updates) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // 增加经验值
  async addExperience(userId, xp) {
    const { data, error } = await supabase.rpc('add_user_xp', {
      user_id: userId,
      xp_amount: xp
    })
    
    if (error) throw error
    return data
  },

  // 增加香草币
  async addVanillaCoins(userId, coins) {
    const { data, error } = await supabase.rpc('add_vanilla_coins', {
      user_id: userId,
      coin_amount: coins
    })
    
    if (error) throw error
    return data
  }
}

// 商品相关 API
export const productAPI = {
  // 获取推荐商品
  async getRecommendations(filters = {}) {
    let query = supabase
      .from('products')
      .select(`
        *,
        merchants (
          id,
          name,
          location
        )
      `)
      .eq('status', 'active')
    
    if (filters.weather) {
      query = query.contains('availability_conditions', { weather: filters.weather })
    }
    
    if (filters.location) {
      query = query.eq('merchants.location', filters.location)
    }
    
    const { data, error } = await query.limit(20)
    
    if (error) throw error
    return data
  },

  // 搜索商品
  async searchProducts(searchTerm, filters = {}) {
    let query = supabase
      .from('products')
      .select(`
        *,
        merchants (
          id,
          name,
          location
        )
      `)
      .eq('status', 'active')
      .ilike('name', `%${searchTerm}%`)
    
    if (filters.category) {
      query = query.eq('category', filters.category)
    }
    
    if (filters.priceRange) {
      query = query.gte('price', filters.priceRange.min)
                   .lte('price', filters.priceRange.max)
    }
    
    const { data, error } = await query
    
    if (error) throw error
    return data
  }
}

// 任务相关 API
export const taskAPI = {
  // 获取用户任务
  async getUserTasks(userId) {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // 完成任务
  async completeTask(taskId, userId) {
    const { data, error } = await supabase.rpc('complete_task', {
      task_id: taskId,
      user_id: userId
    })
    
    if (error) throw error
    return data
  }
}

// 日记相关 API
export const diaryAPI = {
  // 获取日记列表
  async getDiaryEntries(userId, type = null) {
    let query = supabase
      .from('diary_entries')
      .select(`
        *,
        users (
          id,
          username,
          level
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    
    if (type) {
      query = query.eq('type', type)
    }
    
    const { data, error } = await query
    
    if (error) throw error
    return data
  },

  // 创建日记
  async createDiaryEntry(entry) {
    const { data, error } = await supabase
      .from('diary_entries')
      .insert(entry)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // 点赞日记
  async likeDiaryEntry(entryId, userId) {
    const { data, error } = await supabase.rpc('like_diary_entry', {
      entry_id: entryId,
      user_id: userId
    })
    
    if (error) throw error
    return data
  }
}
