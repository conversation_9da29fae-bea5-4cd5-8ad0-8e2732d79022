import React, { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Header from './components/Header'
import HomePage from './pages/HomePage'
import MarketPage from './pages/MarketPage'
import DiaryPage from './pages/DiaryPage'
import MapPage from './pages/MapPage'
import ProfilePage from './pages/ProfilePage'
import TasksPage from './pages/TasksPage'
import { supabase } from './services/supabase'

function App() {
	const [user, setUser] = useState(null)
	const [loading, setLoading] = useState(true)

	useEffect(() => {
		// 获取当前用户会话
		const getSession = async () => {
			const {
				data: { session }
			} = await supabase.auth.getSession()
			setUser(session?.user ?? null)
			setLoading(false)
		}

		getSession()

		// 监听认证状态变化
		const {
			data: { subscription }
		} = supabase.auth.onAuthStateChange((event, session) => {
			setUser(session?.user ?? null)
			setLoading(false)
		})

		return () => subscription.unsubscribe()
	}, [])

	if (loading) {
		return (
			<div className="min-h-screen bg-parchment flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-herb-green mx-auto mb-4"></div>
					<p className="text-herb-green font-medieval">加载中...</p>
				</div>
			</div>
		)
	}

	return (
		<Router>
			<div className="min-h-screen bg-parchment">
				<Header user={user} />
				<main className="container mx-auto px-4 py-8">
					<Routes>
						<Route path="/" element={<HomePage user={user} />} />
						<Route path="/market" element={<MarketPage user={user} />} />
						<Route path="/diary" element={<DiaryPage user={user} />} />
						<Route path="/map" element={<MapPage user={user} />} />
						<Route path="/tasks" element={<TasksPage user={user} />} />
						<Route path="/profile" element={<ProfilePage user={user} />} />
					</Routes>
				</main>
			</div>
		</Router>
	)
}

export default App
