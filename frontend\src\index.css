@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer base {
  body {
    @apply bg-parchment text-herb-green font-body;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-medieval;
  }
}

@layer components {
  /* 中世纪风格按钮 */
  .btn-medieval {
    @apply px-6 py-3 bg-herb-green text-parchment rounded-lg font-medieval 
           hover:bg-rosemary-purple transition-colors duration-300 
           shadow-lg hover:shadow-xl transform hover:-translate-y-1;
  }
  
  /* 卡片样式 */
  .card-medieval {
    @apply bg-parchment border-2 border-sage-gray rounded-lg p-6 
           shadow-lg hover:shadow-xl transition-shadow duration-300;
  }
  
  /* NPC 对话框 */
  .npc-dialog {
    @apply bg-parchment border-2 border-herb-gold rounded-lg p-4 
           relative before:content-[''] before:absolute before:bottom-full 
           before:left-6 before:border-8 before:border-transparent 
           before:border-b-herb-gold;
  }
  
  /* 游戏化进度条 */
  .progress-bar {
    @apply w-full bg-sage-gray rounded-full h-3 overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full bg-gradient-to-r from-herb-green to-herb-gold 
           transition-all duration-500 ease-out;
  }
}

/* 自定义动画 */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-parchment;
}

::-webkit-scrollbar-thumb {
  @apply bg-sage-gray rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-herb-green;
}
