import express from 'express'
import { authMiddleware, optionalAuth } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { supabase } from '../services/supabase.js'

const router = express.Router()

// 获取当前活跃事件
router.get('/active', optionalAuth, asyncHandler(async (req, res) => {
  const now = new Date().toISOString()
  
  const { data: events, error } = await supabase
    .from('events')
    .select('*')
    .eq('status', 'active')
    .lte('start_time', now)
    .gte('end_time', now)
    .order('start_time', { ascending: false })
  
  if (error) throw error
  
  // 处理事件效果，返回对用户有用的信息
  const processedEvents = events.map(event => ({
    ...event,
    time_remaining: new Date(event.end_time) - new Date(),
    is_active: true
  }))
  
  res.json({
    success: true,
    data: processedEvents,
    count: processedEvents.length
  })
}))

// 获取所有事件 (包括历史事件)
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const { status, type, limit = 20, offset = 0 } = req.query
  
  let query = supabase
    .from('events')
    .select('*')
    .order('start_time', { ascending: false })
  
  if (status) {
    query = query.eq('status', status)
  }
  
  if (type) {
    query = query.eq('type', type)
  }
  
  query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)
  
  const { data, error, count } = await query
  
  if (error) throw error
  
  // 添加事件状态信息
  const now = new Date()
  const processedEvents = data.map(event => {
    const startTime = new Date(event.start_time)
    const endTime = new Date(event.end_time)
    
    let eventStatus = 'upcoming'
    if (now >= startTime && now <= endTime) {
      eventStatus = 'active'
    } else if (now > endTime) {
      eventStatus = 'ended'
    }
    
    return {
      ...event,
      event_status: eventStatus,
      time_remaining: eventStatus === 'active' ? endTime - now : null
    }
  })
  
  res.json({
    success: true,
    data: processedEvents,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: count
    }
  })
}))

// 获取事件详情
router.get('/:id', optionalAuth, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const { data: event, error } = await supabase
    .from('events')
    .select('*')
    .eq('id', id)
    .single()
  
  if (error) {
    return res.status(404).json({
      success: false,
      error: '事件不存在'
    })
  }
  
  // 添加事件状态信息
  const now = new Date()
  const startTime = new Date(event.start_time)
  const endTime = new Date(event.end_time)
  
  let eventStatus = 'upcoming'
  if (now >= startTime && now <= endTime) {
    eventStatus = 'active'
  } else if (now > endTime) {
    eventStatus = 'ended'
  }
  
  const processedEvent = {
    ...event,
    event_status: eventStatus,
    time_remaining: eventStatus === 'active' ? endTime - now : null,
    duration: endTime - startTime
  }
  
  res.json({
    success: true,
    data: processedEvent
  })
}))

// 参与事件 (获得事件奖励)
router.post('/:id/participate', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  
  const { data: event, error: eventError } = await supabase
    .from('events')
    .select('*')
    .eq('id', id)
    .single()
  
  if (eventError || !event) {
    return res.status(404).json({
      success: false,
      error: '事件不存在'
    })
  }
  
  // 检查事件是否活跃
  const now = new Date()
  const startTime = new Date(event.start_time)
  const endTime = new Date(event.end_time)
  
  if (now < startTime || now > endTime || event.status !== 'active') {
    return res.status(400).json({
      success: false,
      error: '事件未开始或已结束'
    })
  }
  
  // 检查用户是否已参与过此事件
  // TODO: 可以添加参与记录表来跟踪用户参与情况
  
  // 处理事件奖励
  const rewards = event.rewards || {}
  const messages = []
  
  if (rewards.xp) {
    await supabase.rpc('add_user_xp', {
      user_id: req.user.id,
      xp_amount: rewards.xp
    })
    messages.push(`获得 ${rewards.xp} 经验值`)
  }
  
  if (rewards.coins) {
    await supabase.rpc('add_vanilla_coins', {
      user_id: req.user.id,
      coin_amount: rewards.coins
    })
    messages.push(`获得 ${rewards.coins} 香草币`)
  }
  
  if (rewards.badges && Array.isArray(rewards.badges)) {
    for (const badge of rewards.badges) {
      await supabase
        .from('users')
        .update({
          badges: supabase.raw(`
            CASE 
              WHEN badges @> '[{"id": "${badge.id}"}]'::jsonb 
              THEN badges 
              ELSE badges || '[{"id": "${badge.id}", "name": "${badge.name}", "unlocked_at": "' + NOW() + '"}]'::jsonb
            END
          `)
        })
        .eq('id', req.user.id)
      
      messages.push(`解锁徽章：${badge.name}`)
    }
  }
  
  // 发送参与通知
  await supabase
    .from('messages')
    .insert({
      sender_id: req.user.id,
      recipient_id: req.user.id,
      title: '事件参与成功',
      content: `成功参与事件：${event.title}！${messages.join('，')}。`,
      type: 'system'
    })
  
  res.json({
    success: true,
    data: {
      event_title: event.title,
      rewards,
      messages
    },
    message: `成功参与事件：${event.title}！${messages.join('，')}。`
  })
}))

// 创建事件 (管理员功能)
router.post('/', authMiddleware, asyncHandler(async (req, res) => {
  const { 
    title, 
    description, 
    type, 
    trigger_conditions, 
    effects, 
    rewards,
    start_time,
    end_time
  } = req.body
  
  // 检查管理员权限
  const { data: userProfile } = await supabase
    .from('users')
    .select('role')
    .eq('id', req.user.id)
    .single()
  
  if (userProfile?.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: '权限不足'
    })
  }
  
  const { data: event, error } = await supabase
    .from('events')
    .insert({
      title,
      description,
      type,
      trigger_conditions,
      effects,
      rewards,
      start_time,
      end_time,
      status: 'active'
    })
    .select()
    .single()
  
  if (error) throw error
  
  res.status(201).json({
    success: true,
    data: event,
    message: '事件创建成功'
  })
}))

// 更新事件状态 (管理员功能)
router.put('/:id/status', authMiddleware, asyncHandler(async (req, res) => {
  const { id } = req.params
  const { status } = req.body
  
  // 检查管理员权限
  const { data: userProfile } = await supabase
    .from('users')
    .select('role')
    .eq('id', req.user.id)
    .single()
  
  if (userProfile?.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: '权限不足'
    })
  }
  
  const { data: event, error } = await supabase
    .from('events')
    .update({ status })
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    return res.status(404).json({
      success: false,
      error: '事件不存在'
    })
  }
  
  res.json({
    success: true,
    data: event,
    message: '事件状态更新成功'
  })
}))

// 触发天气事件 (系统调用)
router.post('/trigger/weather', asyncHandler(async (req, res) => {
  const { weather, location } = req.body
  
  // 根据天气条件触发相应事件
  const weatherEvents = {
    'sunny': {
      title: '晴日集市',
      description: '阳光明媚的日子里，所有草本植物的价格下降10%',
      type: 'weather',
      effects: { discount: { category: '草本香料', percentage: 10 } },
      rewards: { xp_bonus: 20 },
      duration_hours: 24
    },
    'rainy': {
      title: '雨夜诗会',
      description: '雨夜时分，发布日记可获得额外经验值',
      type: 'weather',
      effects: { xp_multiplier: { action: 'diary_post', multiplier: 2 } },
      rewards: { coins: 50 },
      duration_hours: 12
    },
    'cloudy': {
      title: '云雾缭绕',
      description: '多云的天气让探索变得更加神秘',
      type: 'weather',
      effects: { exploration_bonus: { multiplier: 1.5 } },
      rewards: { xp: 30 },
      duration_hours: 18
    }
  }
  
  const eventTemplate = weatherEvents[weather]
  if (!eventTemplate) {
    return res.status(400).json({
      success: false,
      error: '不支持的天气类型'
    })
  }
  
  // 检查是否已有相同类型的活跃事件
  const now = new Date()
  const { data: existingEvents } = await supabase
    .from('events')
    .select('id')
    .eq('type', 'weather')
    .eq('status', 'active')
    .lte('start_time', now.toISOString())
    .gte('end_time', now.toISOString())
  
  if (existingEvents && existingEvents.length > 0) {
    return res.status(400).json({
      success: false,
      error: '已有活跃的天气事件'
    })
  }
  
  // 创建新的天气事件
  const startTime = new Date()
  const endTime = new Date(startTime.getTime() + eventTemplate.duration_hours * 60 * 60 * 1000)
  
  const { data: event, error } = await supabase
    .from('events')
    .insert({
      title: eventTemplate.title,
      description: eventTemplate.description,
      type: eventTemplate.type,
      trigger_conditions: { weather, location },
      effects: eventTemplate.effects,
      rewards: eventTemplate.rewards,
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      status: 'active'
    })
    .select()
    .single()
  
  if (error) throw error
  
  res.status(201).json({
    success: true,
    data: event,
    message: `天气事件 ${eventTemplate.title} 已触发`
  })
}))

// 获取事件统计
router.get('/stats/summary', optionalAuth, asyncHandler(async (req, res) => {
  const now = new Date().toISOString()
  
  const [
    { count: activeEvents },
    { count: upcomingEvents },
    { count: totalEvents }
  ] = await Promise.all([
    supabase.from('events').select('*', { count: 'exact', head: true })
      .eq('status', 'active')
      .lte('start_time', now)
      .gte('end_time', now),
    supabase.from('events').select('*', { count: 'exact', head: true })
      .eq('status', 'active')
      .gt('start_time', now),
    supabase.from('events').select('*', { count: 'exact', head: true })
  ])
  
  res.json({
    success: true,
    data: {
      activeEvents,
      upcomingEvents,
      totalEvents,
      endedEvents: totalEvents - activeEvents - upcomingEvents
    }
  })
}))

export default router
