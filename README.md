# 斯卡布罗集市 (Scarborough Fair)

> 一个能"众筹生活"的市集

## 项目概述

斯卡布罗集市是一个融合多租户架构、传统集市文化和游戏化元素的 C2C 电商平台，赋能程序员通过开发和运营实现创业。平台以《Scarborough Fair》歌词的草本意象（芜荽、鼠尾草、迷迭香、百里香）为核心，结合中世纪集市风格，提供诗意的二手交易和社区互动体验。

## 技术栈

- **前端**: React 18.2.0, Tailwind CSS
- **后端**: Node.js, Express
- **数据库**: Supabase (PostgreSQL)
- **地图**: OpenStreetMap
- **支付**: Stripe
- **部署**: Vercel (前端), AWS (后端)

## 项目结构

```
/scarborough-fair
├── /frontend          # React 前端应用
├── /backend           # Node.js 后端服务
├── /docs              # API 文档和教程
├── /scripts           # 部署和工具脚本
├── /assets            # 静态资源 (NPC 图像等)
└── README.md
```

## 核心功能

### 🏪 集市模块
- 多租户商户系统
- 商品展示与搜索
- 智能推荐算法

### 🎮 游戏化系统
- 等级与经验值
- 香草币虚拟货币
- 动态事件与任务

### 📖 日记系统
- 香草日记
- 浪人诗签
- 晨雾行者暗线

### 🗺️ 探索系统
- 集市地图
- 遗忘之地
- 地点解锁

## 色彩设计

- **草绿色**: #4A7043 (主色调)
- **鼠尾灰**: #B0B7A4 (次要色)
- **草本金黄**: #D4A017 (强调色)
- **迷迭香紫**: #6B4E71 (深色)
- **羊皮纸白**: #F5E8C7 (背景色)

## NPC 角色

- **莱瑞克**: 游吟诗人，弹奏鲁特琴
- **芙萝拉**: 草本商人，挥手致意
- **凯恩**: 伶仃猎手，拉弓瞄准
- **帕西**: 神秘角色，与晨雾行者相关

## 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn
- Supabase 账户

### 安装依赖
```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../backend
npm install
```

### 环境配置
1. 复制 `.env.example` 到 `.env`
2. 配置 Supabase 连接信息
3. 配置 Stripe 密钥

### 启动开发服务器
```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务
cd frontend
npm start
```

## 开发指南

详细的开发文档请参考 `/docs` 目录：
- [API 文档](./docs/api.md)
- [数据库设计](./docs/database.md)
- [前端组件](./docs/components.md)
- [游戏化机制](./docs/gamification.md)

## 贡献指南

欢迎程序员加入斯卡布罗集市的开发！请查看 [贡献指南](./docs/contributing.md)。

## 许可证

MIT License

---

*"Are you going to Scarborough Fair? Parsley, sage, rosemary and thyme..."*
