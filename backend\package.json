{"name": "scarborough-fair-backend", "version": "0.1.0", "description": "斯卡布罗集市后端服务", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.38.4", "stripe": "^14.7.0", "node-cron": "^3.0.3", "axios": "^1.6.2", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "@babel/preset-env": "^7.23.6"}, "keywords": ["marketplace", "c2c", "medieval", "gamification", "supabase"], "author": "Scarborough Fair Team", "license": "MIT"}